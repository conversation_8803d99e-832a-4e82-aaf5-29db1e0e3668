"""
Real-time Image Sharpening Pipeline

This module implements the real-time video processing pipeline with webcam capture,
frame preprocessing, model inference, and output rendering for video conferencing.
"""

import cv2
import torch
import torch.nn.functional as F
import numpy as np
from PIL import Image
import time
import argparse
import yaml
from pathlib import Path
from typing import Optional, Tuple, Dict
import threading
import queue
from collections import deque

from ..models.student_models import StudentModelFactory
from ..evaluation.metrics import MetricCalculator


class FrameBuffer:
    """Thread-safe frame buffer for real-time processing."""
    
    def __init__(self, maxsize: int = 10):
        self.buffer = queue.Queue(maxsize=maxsize)
        self.lock = threading.Lock()
    
    def put(self, frame: np.ndarray) -> bool:
        """Add frame to buffer. Returns False if buffer is full."""
        try:
            self.buffer.put_nowait(frame)
            return True
        except queue.Full:
            return False
    
    def get(self) -> Optional[np.ndarray]:
        """Get frame from buffer. Returns None if empty."""
        try:
            return self.buffer.get_nowait()
        except queue.Empty:
            return None
    
    def clear(self):
        """Clear all frames from buffer."""
        with self.lock:
            while not self.buffer.empty():
                try:
                    self.buffer.get_nowait()
                except queue.Empty:
                    break


class RealtimeSharpener:
    """
    Real-time image sharpening system for video conferencing.
    
    Provides webcam capture, real-time inference, and display functionality
    optimized for 30-60 fps performance.
    """
    
    def __init__(
        self,
        model_path: str,
        config_path: str = "configs/config.yaml",
        camera_id: int = 0,
        target_fps: int = 30
    ):
        """
        Initialize the real-time sharpener.
        
        Args:
            model_path: Path to trained student model
            config_path: Path to configuration file
            camera_id: Camera device ID
            target_fps: Target frames per second
        """
        self.model_path = model_path
        self.camera_id = camera_id
        self.target_fps = target_fps
        self.frame_time = 1.0 / target_fps
        
        # Load configuration
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        # Initialize device
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"Using device: {self.device}")
        
        # Load model
        self.model = self._load_model()
        
        # Initialize camera
        self.cap = None
        self.is_running = False
        
        # Frame buffers
        self.input_buffer = FrameBuffer(maxsize=5)
        self.output_buffer = FrameBuffer(maxsize=5)
        
        # Performance tracking
        self.fps_counter = deque(maxlen=30)
        self.inference_times = deque(maxlen=30)
        
        # Preprocessing transforms
        self.preprocess = self._create_preprocessing()
        
        # Metrics calculator (optional)
        self.metrics_calculator = MetricCalculator(str(self.device))
    
    def _load_model(self) -> torch.nn.Module:
        """Load the trained student model."""
        # Create model architecture
        model = StudentModelFactory.create_student(
            self.config['model']['student']['name'],
            scale_factor=self.config['model']['student']['scale_factor'],
            base_channels=self.config['model']['student']['channels'],
            num_blocks=self.config['model']['student']['num_blocks'],
            use_attention=self.config['model']['student']['use_attention']
        )
        
        # Load trained weights
        if Path(self.model_path).exists():
            checkpoint = torch.load(self.model_path, map_location=self.device)
            if 'student_state_dict' in checkpoint:
                model.load_state_dict(checkpoint['student_state_dict'])
            else:
                model.load_state_dict(checkpoint)
            print(f"Loaded model from {self.model_path}")
        else:
            print(f"Warning: Model file {self.model_path} not found. Using random weights.")
        
        model.to(self.device)
        model.eval()
        
        # Optimize for inference
        if hasattr(torch.jit, 'script'):
            try:
                model = torch.jit.script(model)
                print("Model optimized with TorchScript")
            except Exception as e:
                print(f"TorchScript optimization failed: {e}")
        
        return model
    
    def _create_preprocessing(self):
        """Create preprocessing pipeline."""
        def preprocess(frame: np.ndarray) -> torch.Tensor:
            # Convert BGR to RGB
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            # Convert to PIL Image
            pil_image = Image.fromarray(frame_rgb)
            
            # Convert to tensor and normalize
            tensor = torch.from_numpy(np.array(pil_image)).float() / 255.0
            tensor = tensor.permute(2, 0, 1).unsqueeze(0)  # (1, C, H, W)
            
            return tensor.to(self.device)
        
        return preprocess
    
    def _postprocess(self, tensor: torch.Tensor) -> np.ndarray:
        """Convert model output back to displayable image."""
        # Clamp values to [0, 1]
        tensor = torch.clamp(tensor, 0, 1)
        
        # Convert to numpy
        image = tensor.squeeze(0).permute(1, 2, 0).cpu().numpy()
        
        # Convert to uint8
        image = (image * 255).astype(np.uint8)
        
        # Convert RGB to BGR for OpenCV
        image_bgr = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
        
        return image_bgr
    
    def _initialize_camera(self) -> bool:
        """Initialize camera capture."""
        self.cap = cv2.VideoCapture(self.camera_id)
        
        if not self.cap.isOpened():
            print(f"Error: Could not open camera {self.camera_id}")
            return False
        
        # Set camera properties
        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1920)
        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 1080)
        self.cap.set(cv2.CAP_PROP_FPS, self.target_fps)
        self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # Reduce latency
        
        # Get actual camera properties
        width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = self.cap.get(cv2.CAP_PROP_FPS)
        
        print(f"Camera initialized: {width}x{height} @ {fps} FPS")
        return True
    
    def _capture_thread(self):
        """Thread function for capturing frames."""
        while self.is_running:
            ret, frame = self.cap.read()
            if ret:
                # Add to input buffer (drop if full)
                self.input_buffer.put(frame)
            else:
                print("Warning: Failed to capture frame")
                time.sleep(0.01)
    
    def _inference_thread(self):
        """Thread function for model inference."""
        while self.is_running:
            # Get frame from input buffer
            frame = self.input_buffer.get()
            if frame is None:
                time.sleep(0.001)
                continue
            
            # Perform inference
            start_time = time.time()
            
            with torch.no_grad():
                # Preprocess
                input_tensor = self.preprocess(frame)
                
                # Model inference
                output_tensor = self.model(input_tensor)
                
                # Postprocess
                enhanced_frame = self._postprocess(output_tensor)
            
            inference_time = time.time() - start_time
            self.inference_times.append(inference_time)
            
                # Add to output buffer
            self.output_buffer.put(enhanced_frame)
    
    def _display_thread(self):
        """Thread function for displaying results."""
        while self.is_running:
            # Get enhanced frame
            enhanced_frame = self.output_buffer.get()
            if enhanced_frame is None:
                time.sleep(0.001)
                continue
            
            # Calculate FPS
            current_time = time.time()
            self.fps_counter.append(current_time)
            
            # Display frame with overlay
            display_frame = self._add_overlay(enhanced_frame)
            
            cv2.imshow('Real-time Image Sharpening', display_frame)
            
            # Handle key presses
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                self.stop()
                break
            elif key == ord('s'):
                # Save current frame
                timestamp = int(time.time())
                filename = f"enhanced_frame_{timestamp}.png"
                cv2.imwrite(filename, enhanced_frame)
                print(f"Saved frame: {filename}")
    
    def _add_overlay(self, frame: np.ndarray) -> np.ndarray:
        """Add performance overlay to frame."""
        overlay_frame = frame.copy()
        
        # Calculate current FPS
        if len(self.fps_counter) > 1:
            time_diff = self.fps_counter[-1] - self.fps_counter[0]
            current_fps = (len(self.fps_counter) - 1) / time_diff if time_diff > 0 else 0
        else:
            current_fps = 0
        
        # Calculate average inference time
        avg_inference_time = np.mean(self.inference_times) if self.inference_times else 0
        
        # Add text overlay
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.7
        color = (0, 255, 0)  # Green
        thickness = 2
        
        # FPS
        fps_text = f"FPS: {current_fps:.1f}"
        cv2.putText(overlay_frame, fps_text, (10, 30), font, font_scale, color, thickness)
        
        # Inference time
        inference_text = f"Inference: {avg_inference_time*1000:.1f}ms"
        cv2.putText(overlay_frame, inference_text, (10, 60), font, font_scale, color, thickness)
        
        # Model info
        model_text = f"Model: {self.config['model']['student']['name']}"
        cv2.putText(overlay_frame, model_text, (10, 90), font, font_scale, color, thickness)
        
        # Instructions
        instructions = "Press 'q' to quit, 's' to save frame"
        cv2.putText(overlay_frame, instructions, (10, overlay_frame.shape[0] - 20), 
                   font, 0.5, color, 1)
        
        return overlay_frame
    
    def start(self):
        """Start real-time processing."""
        print("Starting real-time image sharpening...")
        
        # Initialize camera
        if not self._initialize_camera():
            return
        
        self.is_running = True
        
        # Start threads
        capture_thread = threading.Thread(target=self._capture_thread)
        inference_thread = threading.Thread(target=self._inference_thread)
        display_thread = threading.Thread(target=self._display_thread)
        
        capture_thread.start()
        inference_thread.start()
        display_thread.start()
        
        print("Real-time processing started. Press 'q' to quit.")
        
        # Wait for threads to complete
        try:
            display_thread.join()
            capture_thread.join()
            inference_thread.join()
        except KeyboardInterrupt:
            print("Interrupted by user")
            self.stop()
    
    def stop(self):
        """Stop real-time processing."""
        print("Stopping real-time processing...")
        self.is_running = False
        
        if self.cap is not None:
            self.cap.release()
        
        cv2.destroyAllWindows()
        
        # Clear buffers
        self.input_buffer.clear()
        self.output_buffer.clear()
    
    def process_single_image(self, image_path: str, output_path: str):
        """
        Process a single image file.
        
        Args:
            image_path: Path to input image
            output_path: Path to save enhanced image
        """
        # Load image
        image = cv2.imread(image_path)
        if image is None:
            print(f"Error: Could not load image {image_path}")
            return
        
        print(f"Processing image: {image_path}")
        
        # Preprocess
        input_tensor = self.preprocess(image)
        
        # Inference
        start_time = time.time()
        with torch.no_grad():
            output_tensor = self.model(input_tensor)
        inference_time = time.time() - start_time
        
        # Postprocess
        enhanced_image = self._postprocess(output_tensor)
        
        # Save result
        cv2.imwrite(output_path, enhanced_image)
        
        print(f"Enhanced image saved: {output_path}")
        print(f"Inference time: {inference_time*1000:.2f}ms")


def main():
    """Main function for real-time sharpening."""
    parser = argparse.ArgumentParser(description="Real-time Image Sharpening")
    parser.add_argument("--model", type=str, required=True, help="Path to trained model")
    parser.add_argument("--config", type=str, default="configs/config.yaml", help="Config file")
    parser.add_argument("--camera", type=int, default=0, help="Camera ID")
    parser.add_argument("--fps", type=int, default=30, help="Target FPS")
    parser.add_argument("--image", type=str, help="Process single image instead of webcam")
    parser.add_argument("--output", type=str, help="Output path for single image")
    
    args = parser.parse_args()
    
    # Create sharpener
    sharpener = RealtimeSharpener(
        model_path=args.model,
        config_path=args.config,
        camera_id=args.camera,
        target_fps=args.fps
    )
    
    if args.image:
        # Process single image
        output_path = args.output or args.image.replace('.', '_enhanced.')
        sharpener.process_single_image(args.image, output_path)
    else:
        # Start real-time processing
        try:
            sharpener.start()
        except KeyboardInterrupt:
            sharpener.stop()


if __name__ == "__main__":
    main()
