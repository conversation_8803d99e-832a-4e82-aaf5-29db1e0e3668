"""
Sample Data Downloader

Downloads sample images from various categories for testing and demonstration.
This script helps users get started with sample data for training and evaluation.
"""

import os
import requests
from pathlib import Path
from typing import List, Dict
import cv2
import numpy as np
from tqdm import tqdm


class SampleDataDownloader:
    """Downloads and organizes sample data for image sharpening."""
    
    def __init__(self, output_dir: str = "datasets/raw"):
        """
        Initialize the sample data downloader.
        
        Args:
            output_dir: Directory to save downloaded images
        """
        self.output_dir = Path(output_dir)
        self.categories = {
            'people': [],
            'nature': [],
            'text': [],
            'animals': [],
            'games': []
        }
        
        # Create category directories
        for category in self.categories.keys():
            (self.output_dir / category).mkdir(parents=True, exist_ok=True)
    
    def generate_synthetic_images(self):
        """
        Generate synthetic test images for each category.
        This is useful when internet access is limited or for quick testing.
        """
        print("Generating synthetic test images...")
        
        # Generate people-like images (faces/portraits)
        self._generate_face_like_images()
        
        # Generate nature-like images (landscapes/textures)
        self._generate_nature_like_images()
        
        # Generate text images
        self._generate_text_images()
        
        # Generate animal-like images
        self._generate_animal_like_images()
        
        # Generate game-like images (geometric patterns)
        self._generate_game_like_images()
    
    def _generate_face_like_images(self):
        """Generate synthetic face-like images."""
        for i in range(10):
            # Create a face-like pattern with gradients and circles
            img = np.zeros((1080, 1920, 3), dtype=np.uint8)
            
            # Background gradient
            for y in range(1080):
                for x in range(1920):
                    img[y, x] = [
                        int(120 + 50 * np.sin(x * 0.01)),
                        int(100 + 40 * np.cos(y * 0.01)),
                        int(80 + 30 * np.sin((x + y) * 0.005))
                    ]
            
            # Add face-like features (circles for eyes, etc.)
            center_x, center_y = 1920 // 2, 1080 // 2
            
            # Face outline
            cv2.ellipse(img, (center_x, center_y), (300, 400), 0, 0, 360, (200, 180, 160), -1)
            
            # Eyes
            cv2.circle(img, (center_x - 80, center_y - 50), 30, (50, 50, 50), -1)
            cv2.circle(img, (center_x + 80, center_y - 50), 30, (50, 50, 50), -1)
            
            # Nose
            cv2.ellipse(img, (center_x, center_y + 20), (15, 30), 0, 0, 360, (150, 130, 110), -1)
            
            # Mouth
            cv2.ellipse(img, (center_x, center_y + 80), (40, 20), 0, 0, 180, (100, 80, 80), 3)
            
            # Add some noise for realism
            noise = np.random.normal(0, 10, img.shape).astype(np.int16)
            img = np.clip(img.astype(np.int16) + noise, 0, 255).astype(np.uint8)
            
            filename = self.output_dir / "people" / f"synthetic_person_{i:03d}.png"
            cv2.imwrite(str(filename), img)
    
    def _generate_nature_like_images(self):
        """Generate synthetic nature-like images."""
        for i in range(10):
            img = np.zeros((1080, 1920, 3), dtype=np.uint8)
            
            # Sky gradient
            for y in range(400):
                color_intensity = int(255 * (1 - y / 400))
                img[y, :] = [color_intensity, color_intensity, 255]
            
            # Ground
            for y in range(400, 1080):
                green_intensity = int(100 + 50 * np.random.random())
                img[y, :] = [0, green_intensity, 0]
            
            # Add some "trees" (triangles)
            for _ in range(20):
                x = np.random.randint(0, 1920)
                y = np.random.randint(300, 800)
                size = np.random.randint(50, 200)
                
                pts = np.array([[x, y], [x - size//2, y + size], [x + size//2, y + size]], np.int32)
                cv2.fillPoly(img, [pts], (0, 80, 0))
            
            # Add texture noise
            noise = np.random.normal(0, 15, img.shape).astype(np.int16)
            img = np.clip(img.astype(np.int16) + noise, 0, 255).astype(np.uint8)
            
            filename = self.output_dir / "nature" / f"synthetic_nature_{i:03d}.png"
            cv2.imwrite(str(filename), img)
    
    def _generate_text_images(self):
        """Generate synthetic text images."""
        fonts = [cv2.FONT_HERSHEY_SIMPLEX, cv2.FONT_HERSHEY_PLAIN, cv2.FONT_HERSHEY_COMPLEX]
        texts = [
            "Video Conference Quality Test",
            "Image Sharpening Evaluation",
            "Knowledge Distillation Demo",
            "Real-time Processing Test",
            "SSIM > 90% Target Quality",
            "30-60 FPS Performance Goal",
            "Teacher Student Architecture",
            "Deep Learning Enhancement",
            "Computer Vision Pipeline",
            "Webcam Input Processing"
        ]
        
        for i, text in enumerate(texts):
            img = np.ones((1080, 1920, 3), dtype=np.uint8) * 255  # White background
            
            # Add some background texture
            for _ in range(100):
                x, y = np.random.randint(0, 1920), np.random.randint(0, 1080)
                cv2.circle(img, (x, y), np.random.randint(1, 3), (240, 240, 240), -1)
            
            # Add main text
            font = fonts[i % len(fonts)]
            font_scale = 2.0 + np.random.random()
            thickness = 3
            
            # Get text size for centering
            (text_width, text_height), _ = cv2.getTextSize(text, font, font_scale, thickness)
            x = (1920 - text_width) // 2
            y = (1080 + text_height) // 2
            
            cv2.putText(img, text, (x, y), font, font_scale, (0, 0, 0), thickness)
            
            # Add some smaller text
            small_texts = ["High Resolution", "Sharp Details", "Clear Text"]
            for j, small_text in enumerate(small_texts):
                small_y = y + 100 + j * 50
                cv2.putText(img, small_text, (x, small_y), font, 1.0, (100, 100, 100), 2)
            
            filename = self.output_dir / "text" / f"synthetic_text_{i:03d}.png"
            cv2.imwrite(str(filename), img)
    
    def _generate_animal_like_images(self):
        """Generate synthetic animal-like images."""
        for i in range(10):
            img = np.zeros((1080, 1920, 3), dtype=np.uint8)
            
            # Random background
            bg_color = np.random.randint(50, 150, 3)
            img[:] = bg_color
            
            # Create animal-like shapes (ellipses and circles)
            center_x, center_y = 1920 // 2, 1080 // 2
            
            # Body
            body_color = np.random.randint(100, 200, 3)
            cv2.ellipse(img, (center_x, center_y), (200, 150), 0, 0, 360, body_color.tolist(), -1)
            
            # Head
            head_color = body_color + np.random.randint(-30, 30, 3)
            head_color = np.clip(head_color, 0, 255)
            cv2.circle(img, (center_x, center_y - 100), 80, head_color.tolist(), -1)
            
            # Features (eyes, ears, etc.)
            cv2.circle(img, (center_x - 30, center_y - 120), 10, (0, 0, 0), -1)
            cv2.circle(img, (center_x + 30, center_y - 120), 10, (0, 0, 0), -1)
            
            # Add fur-like texture
            for _ in range(1000):
                x = np.random.randint(center_x - 250, center_x + 250)
                y = np.random.randint(center_y - 200, center_y + 200)
                if (x - center_x)**2 + (y - center_y)**2 < 40000:  # Within animal shape
                    cv2.circle(img, (x, y), 1, (body_color + np.random.randint(-20, 20, 3)).tolist(), -1)
            
            filename = self.output_dir / "animals" / f"synthetic_animal_{i:03d}.png"
            cv2.imwrite(str(filename), img)
    
    def _generate_game_like_images(self):
        """Generate synthetic game-like images with geometric patterns."""
        for i in range(10):
            img = np.zeros((1080, 1920, 3), dtype=np.uint8)
            
            # Create game-like geometric patterns
            colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0), (255, 0, 255), (0, 255, 255)]
            
            # Grid pattern
            for x in range(0, 1920, 100):
                cv2.line(img, (x, 0), (x, 1080), (50, 50, 50), 2)
            for y in range(0, 1080, 100):
                cv2.line(img, (0, y), (1920, y), (50, 50, 50), 2)
            
            # Add geometric shapes
            for _ in range(20):
                shape_type = np.random.choice(['rectangle', 'circle', 'triangle'])
                color = colors[np.random.randint(0, len(colors))]
                
                if shape_type == 'rectangle':
                    x1, y1 = np.random.randint(0, 1920), np.random.randint(0, 1080)
                    x2, y2 = x1 + np.random.randint(50, 200), y1 + np.random.randint(50, 200)
                    cv2.rectangle(img, (x1, y1), (min(x2, 1919), min(y2, 1079)), color, -1)
                
                elif shape_type == 'circle':
                    center = (np.random.randint(0, 1920), np.random.randint(0, 1080))
                    radius = np.random.randint(20, 100)
                    cv2.circle(img, center, radius, color, -1)
                
                elif shape_type == 'triangle':
                    pts = np.random.randint(0, min(1920, 1080), (3, 2))
                    cv2.fillPoly(img, [pts], color)
            
            filename = self.output_dir / "games" / f"synthetic_game_{i:03d}.png"
            cv2.imwrite(str(filename), img)
    
    def create_sample_dataset(self):
        """Create a complete sample dataset."""
        print(f"Creating sample dataset in {self.output_dir}")
        
        # Generate synthetic images
        self.generate_synthetic_images()
        
        # Create a summary
        total_images = 0
        for category in self.categories.keys():
            category_path = self.output_dir / category
            image_count = len(list(category_path.glob("*.png")))
            total_images += image_count
            print(f"{category}: {image_count} images")
        
        print(f"Total: {total_images} sample images created")
        print("\nTo process this data for training, run:")
        print(f"python src/data/dataset_generator.py --input_dir {self.output_dir} --output_dir datasets/processed")


def main():
    """Main function to create sample dataset."""
    downloader = SampleDataDownloader()
    downloader.create_sample_dataset()


if __name__ == "__main__":
    main()
