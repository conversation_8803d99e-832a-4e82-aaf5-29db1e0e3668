"""
Student Models for Knowledge Distillation

This module implements ultra-lightweight CNN architectures optimized for
real-time inference (30-60 fps) while maintaining high accuracy through
knowledge distillation from teacher models.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import List, Tuple, Optional
import math


class DepthwiseSeparableConv(nn.Module):
    """Depthwise separable convolution for efficient computation."""
    
    def __init__(self, in_channels: int, out_channels: int, kernel_size: int = 3, stride: int = 1):
        super(DepthwiseSeparableConv, self).__init__()
        self.depthwise = nn.Conv2d(
            in_channels, in_channels, kernel_size, 
            stride=stride, padding=kernel_size//2, groups=in_channels, bias=False
        )
        self.pointwise = nn.Conv2d(in_channels, out_channels, 1, bias=False)
        self.bn = nn.BatchNorm2d(out_channels)
        self.relu = nn.ReLU6(inplace=True)
    
    def forward(self, x):
        x = self.depthwise(x)
        x = self.pointwise(x)
        x = self.bn(x)
        x = self.relu(x)
        return x


class LightweightResidualBlock(nn.Module):
    """Lightweight residual block with depthwise separable convolutions."""
    
    def __init__(self, channels: int, use_attention: bool = False):
        super(LightweightResidualBlock, self).__init__()
        self.conv1 = DepthwiseSeparableConv(channels, channels)
        self.conv2 = nn.Conv2d(channels, channels, 3, padding=1, bias=False)
        self.bn = nn.BatchNorm2d(channels)
        self.use_attention = use_attention
        
        if use_attention:
            self.attention = nn.Sequential(
                nn.AdaptiveAvgPool2d(1),
                nn.Conv2d(channels, channels // 4, 1),
                nn.ReLU(inplace=True),
                nn.Conv2d(channels // 4, channels, 1),
                nn.Sigmoid()
            )
    
    def forward(self, x):
        residual = x
        out = self.conv1(x)
        out = self.bn(self.conv2(out))
        
        if self.use_attention:
            att = self.attention(out)
            out = out * att
        
        out += residual
        return F.relu(out, inplace=True)


class EfficientUpsampling(nn.Module):
    """Efficient upsampling using sub-pixel convolution."""
    
    def __init__(self, in_channels: int, out_channels: int, scale_factor: int = 2):
        super(EfficientUpsampling, self).__init__()
        self.conv = nn.Conv2d(
            in_channels, 
            out_channels * (scale_factor ** 2), 
            3, padding=1
        )
        self.pixel_shuffle = nn.PixelShuffle(scale_factor)
        self.relu = nn.ReLU(inplace=True)
    
    def forward(self, x):
        x = self.conv(x)
        x = self.pixel_shuffle(x)
        x = self.relu(x)
        return x


class MobileNetV2Student(nn.Module):
    """
    MobileNetV2-inspired student model for real-time image sharpening.
    
    Ultra-lightweight architecture optimized for mobile and edge devices.
    """
    
    def __init__(
        self,
        scale_factor: int = 4,
        num_channels: int = 3,
        base_channels: int = 32,
        num_blocks: int = 6
    ):
        super(MobileNetV2Student, self).__init__()
        self.scale_factor = scale_factor
        
        # Initial feature extraction
        self.head = nn.Sequential(
            nn.Conv2d(num_channels, base_channels, 3, padding=1, bias=False),
            nn.BatchNorm2d(base_channels),
            nn.ReLU6(inplace=True)
        )
        
        # Lightweight residual blocks
        self.body = nn.ModuleList([
            LightweightResidualBlock(base_channels, use_attention=(i % 2 == 0))
            for i in range(num_blocks)
        ])
        
        # Upsampling path
        if scale_factor == 4:
            self.upsampler = nn.Sequential(
                EfficientUpsampling(base_channels, base_channels, 2),
                EfficientUpsampling(base_channels, base_channels, 2)
            )
        elif scale_factor == 2:
            self.upsampler = EfficientUpsampling(base_channels, base_channels, 2)
        
        # Final reconstruction
        self.tail = nn.Conv2d(base_channels, num_channels, 3, padding=1)
    
    def forward(self, x):
        # Feature extraction
        x = self.head(x)
        
        # Residual learning
        for block in self.body:
            x = block(x)
        
        # Upsampling
        x = self.upsampler(x)
        
        # Final reconstruction
        x = self.tail(x)
        
        return x


class TinyStudent(nn.Module):
    """
    Extremely lightweight student model for maximum speed.
    
    Designed for scenarios where speed is more critical than quality.
    """
    
    def __init__(
        self,
        scale_factor: int = 4,
        num_channels: int = 3,
        base_channels: int = 16,
        num_blocks: int = 3
    ):
        super(TinyStudent, self).__init__()
        self.scale_factor = scale_factor
        
        # Minimal feature extraction
        self.head = nn.Conv2d(num_channels, base_channels, 3, padding=1)
        
        # Very few residual blocks
        self.body = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(base_channels, base_channels, 3, padding=1, groups=base_channels),
                nn.Conv2d(base_channels, base_channels, 1),
                nn.ReLU(inplace=True)
            ) for _ in range(num_blocks)
        ])
        
        # Simple upsampling
        if scale_factor == 4:
            self.upsampler = nn.Sequential(
                nn.Conv2d(base_channels, base_channels * 4, 3, padding=1),
                nn.PixelShuffle(2),
                nn.ReLU(inplace=True),
                nn.Conv2d(base_channels, base_channels * 4, 3, padding=1),
                nn.PixelShuffle(2),
                nn.ReLU(inplace=True)
            )
        
        # Output layer
        self.tail = nn.Conv2d(base_channels, num_channels, 1)
    
    def forward(self, x):
        x = F.relu(self.head(x))
        
        for block in self.body:
            residual = x
            x = block(x)
            x += residual
        
        x = self.upsampler(x)
        x = self.tail(x)
        
        return x


class EfficientStudent(nn.Module):
    """
    Balanced student model with good quality-speed tradeoff.
    
    Uses efficient operations while maintaining reasonable quality.
    """
    
    def __init__(
        self,
        scale_factor: int = 4,
        num_channels: int = 3,
        base_channels: int = 48,
        num_blocks: int = 8,
        use_attention: bool = True
    ):
        super(EfficientStudent, self).__init__()
        self.scale_factor = scale_factor
        
        # Feature extraction
        self.head = nn.Sequential(
            nn.Conv2d(num_channels, base_channels, 3, padding=1),
            nn.ReLU(inplace=True)
        )
        
        # Efficient residual blocks
        self.body = nn.ModuleList()
        for i in range(num_blocks):
            if i % 3 == 0 and use_attention:
                # Add attention every 3rd block
                block = nn.Sequential(
                    LightweightResidualBlock(base_channels, use_attention=True),
                    nn.Conv2d(base_channels, base_channels, 1),
                    nn.ReLU(inplace=True)
                )
            else:
                block = LightweightResidualBlock(base_channels, use_attention=False)
            self.body.append(block)
        
        # Progressive upsampling
        if scale_factor == 4:
            self.upsampler = nn.Sequential(
                # First upsampling stage
                nn.Conv2d(base_channels, base_channels * 4, 3, padding=1),
                nn.PixelShuffle(2),
                nn.ReLU(inplace=True),
                
                # Refinement
                DepthwiseSeparableConv(base_channels, base_channels),
                
                # Second upsampling stage
                nn.Conv2d(base_channels, base_channels * 4, 3, padding=1),
                nn.PixelShuffle(2),
                nn.ReLU(inplace=True)
            )
        
        # Final refinement
        self.tail = nn.Sequential(
            nn.Conv2d(base_channels, base_channels, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(base_channels, num_channels, 3, padding=1)
        )
    
    def forward(self, x):
        # Feature extraction
        x = self.head(x)
        
        # Residual learning
        for block in self.body:
            x = block(x)
        
        # Upsampling
        x = self.upsampler(x)
        
        # Final reconstruction
        x = self.tail(x)
        
        return x


class StudentModelFactory:
    """Factory class for creating student models."""
    
    @staticmethod
    def create_student(
        model_name: str,
        scale_factor: int = 4,
        num_channels: int = 3,
        **kwargs
    ) -> nn.Module:
        """
        Create a student model by name.
        
        Args:
            model_name: Name of the student model
            scale_factor: Upscaling factor
            num_channels: Number of input channels
            **kwargs: Additional model-specific parameters
            
        Returns:
            Student model instance
        """
        model_name = model_name.lower()
        
        if model_name == 'mobilenetv2':
            return MobileNetV2Student(
                scale_factor=scale_factor,
                num_channels=num_channels,
                base_channels=kwargs.get('base_channels', 32),
                num_blocks=kwargs.get('num_blocks', 6)
            )
        
        elif model_name == 'tiny':
            return TinyStudent(
                scale_factor=scale_factor,
                num_channels=num_channels,
                base_channels=kwargs.get('base_channels', 16),
                num_blocks=kwargs.get('num_blocks', 3)
            )
        
        elif model_name == 'efficient':
            return EfficientStudent(
                scale_factor=scale_factor,
                num_channels=num_channels,
                base_channels=kwargs.get('base_channels', 48),
                num_blocks=kwargs.get('num_blocks', 8),
                use_attention=kwargs.get('use_attention', True)
            )
        
        else:
            raise ValueError(f"Unknown student model: {model_name}")
    
    @staticmethod
    def get_model_info(model: nn.Module) -> dict:
        """
        Get information about a model.
        
        Args:
            model: PyTorch model
            
        Returns:
            Dictionary with model information
        """
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        return {
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'model_size_mb': total_params * 4 / (1024 * 1024),  # Assuming float32
        }


def benchmark_student_models():
    """Benchmark different student models for speed and size."""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Test input
    x = torch.randn(1, 3, 256, 256).to(device)
    
    models_to_test = ['tiny', 'mobilenetv2', 'efficient']
    
    print("Student Model Benchmarks:")
    print("-" * 60)
    
    for model_name in models_to_test:
        model = StudentModelFactory.create_student(model_name).to(device)
        model.eval()
        
        info = StudentModelFactory.get_model_info(model)
        
        # Warmup
        with torch.no_grad():
            for _ in range(10):
                _ = model(x)
        
        # Timing
        torch.cuda.synchronize() if torch.cuda.is_available() else None
        import time
        start_time = time.time()
        
        with torch.no_grad():
            for _ in range(100):
                output = model(x)
        
        torch.cuda.synchronize() if torch.cuda.is_available() else None
        end_time = time.time()
        
        avg_time = (end_time - start_time) / 100
        fps = 1.0 / avg_time
        
        print(f"{model_name.upper()}:")
        print(f"  Parameters: {info['total_parameters']:,}")
        print(f"  Model Size: {info['model_size_mb']:.2f} MB")
        print(f"  Inference Time: {avg_time*1000:.2f} ms")
        print(f"  FPS: {fps:.1f}")
        print(f"  Output Shape: {output.shape}")
        print()


if __name__ == "__main__":
    benchmark_student_models()
