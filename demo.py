"""
De<PERSON> Script for Image Sharpening with Knowledge Distillation

This script demonstrates the complete pipeline from data generation
to model training and inference.
"""

import os
import sys
import argparse
import yaml
from pathlib import Path

# Add src to path
sys.path.append('src')

from src.data.download_sample_data import SampleDataDownloader
from src.data.dataset_generator import DatasetGenerator
from src.training.distillation_trainer import DistillationTrainer
from src.inference.image_processor import ImageProcessor
from src.evaluation.benchmark import BenchmarkSuite


def setup_demo_environment():
    """Set up the demo environment with sample data."""
    print("Setting up demo environment...")
    
    # Create sample data
    print("1. Generating sample data...")
    downloader = SampleDataDownloader("datasets/raw")
    downloader.create_sample_dataset()
    
    # Generate training dataset
    print("2. Preparing training dataset...")
    generator = DatasetGenerator()
    generator.generate_dataset("datasets/raw", "datasets/processed")
    
    print("Demo environment setup complete!")


def train_demo_model():
    """Train a demo model with reduced parameters for quick demonstration."""
    print("Training demo model...")
    
    # Create a lightweight config for demo
    demo_config = {
        'project': {
            'name': 'image_sharpening_demo',
            'version': '1.0.0'
        },
        'data': {
            'input_resolution': [240, 320],
            'target_resolution': [480, 640],
            'crop_size': [128, 128],
            'batch_size': 4,
            'num_workers': 2,
            'augmentation': {
                'horizontal_flip': 0.5,
                'vertical_flip': 0.0,
                'rotation': 10,
                'brightness': 0.1,
                'contrast': 0.1,
                'saturation': 0.1,
                'hue': 0.05
            }
        },
        'model': {
            'teacher': {
                'name': 'edsr',
                'scale_factor': 2
            },
            'student': {
                'name': 'tiny',
                'scale_factor': 2,
                'channels': 16,
                'num_blocks': 3,
                'use_attention': False
            }
        },
        'training': {
            'epochs': 5,  # Reduced for demo
            'learning_rate': 0.001,
            'weight_decay': 0.0001,
            'scheduler': 'cosine',
            'warmup_epochs': 1,
            'loss': {
                'mse_weight': 1.0,
                'perceptual_weight': 0.1,
                'adversarial_weight': 0.0,
                'distillation_weight': 0.5,
                'ssim_weight': 0.1
            },
            'distillation': {
                'temperature': 4.0,
                'alpha': 0.7,
                'beta': 0.3
            }
        },
        'evaluation': {
            'metrics': ['ssim', 'psnr', 'mse'],
            'target_ssim': 0.85,
            'target_fps': 30
        },
        'inference': {
            'device': 'cuda',
            'batch_size': 1,
            'use_half_precision': False,
            'optimize_for_mobile': False
        },
        'logging': {
            'use_wandb': False,
            'use_tensorboard': True,
            'log_interval': 10,
            'save_interval': 2
        },
        'paths': {
            'datasets': './datasets',
            'models': './models',
            'results': './results',
            'logs': './logs'
        }
    }
    
    # Save demo config
    os.makedirs('configs', exist_ok=True)
    with open('configs/demo_config.yaml', 'w') as f:
        yaml.dump(demo_config, f, default_flow_style=False)
    
    # Train model
    try:
        trainer = DistillationTrainer('configs/demo_config.yaml')
        trainer.train()
        print("Demo model training completed!")
        return True
    except Exception as e:
        print(f"Training failed: {e}")
        return False


def run_demo_inference():
    """Run inference demo with the trained model."""
    print("Running inference demo...")
    
    model_path = "models/student/best_model.pth"
    if not os.path.exists(model_path):
        print(f"Model not found at {model_path}")
        print("Please train the model first or provide a valid model path")
        return
    
    # Create processor
    processor = ImageProcessor(model_path, "configs/demo_config.yaml")
    
    # Find sample images
    sample_dir = Path("datasets/raw")
    sample_images = []
    for category in ['people', 'nature', 'text']:
        category_dir = sample_dir / category
        if category_dir.exists():
            images = list(category_dir.glob("*.png"))[:2]  # Take 2 from each category
            sample_images.extend(images)
    
    if not sample_images:
        print("No sample images found for demo")
        return
    
    # Process sample images
    output_dir = Path("results/demo_output")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"Processing {len(sample_images)} sample images...")
    
    for i, image_path in enumerate(sample_images):
        output_path = output_dir / f"enhanced_{i:02d}_{image_path.name}"
        
        try:
            enhanced_image = processor.process_image(str(image_path), str(output_path))
            print(f"✓ Processed: {image_path.name}")
        except Exception as e:
            print(f"✗ Failed to process {image_path.name}: {e}")
    
    print(f"Demo inference completed! Results saved in {output_dir}")


def run_demo_benchmark():
    """Run a quick benchmark demo."""
    print("Running benchmark demo...")
    
    model_path = "models/student/best_model.pth"
    if not os.path.exists(model_path):
        print("Model not found. Skipping benchmark demo.")
        return
    
    # Create benchmark suite
    benchmark = BenchmarkSuite(model_path, "configs/demo_config.yaml")
    
    # Find test images
    test_dir = "datasets/raw/people"  # Use people category for demo
    if not os.path.exists(test_dir):
        print("Test directory not found. Skipping benchmark demo.")
        return
    
    test_images = list(Path(test_dir).glob("*.png"))[:5]  # Limit for demo
    
    if test_images:
        # Performance benchmark
        print("Running performance benchmark...")
        benchmark.benchmark_performance([str(img) for img in test_images])
        
        # Generate report
        benchmark.generate_report("results/demo_benchmark")
        print("Benchmark demo completed! Results saved in results/demo_benchmark")
    else:
        print("No test images found for benchmark demo")


def main():
    """Main demo function."""
    parser = argparse.ArgumentParser(description="Image Sharpening Demo")
    parser.add_argument("--setup", action="store_true", help="Set up demo environment")
    parser.add_argument("--train", action="store_true", help="Train demo model")
    parser.add_argument("--inference", action="store_true", help="Run inference demo")
    parser.add_argument("--benchmark", action="store_true", help="Run benchmark demo")
    parser.add_argument("--all", action="store_true", help="Run complete demo pipeline")
    parser.add_argument("--ui", action="store_true", help="Launch Streamlit UI")
    
    args = parser.parse_args()
    
    if args.all:
        # Run complete pipeline
        print("🚀 Running complete demo pipeline...")
        
        # Setup
        setup_demo_environment()
        
        # Train (with user confirmation due to time)
        train_confirm = input("Training may take several minutes. Continue? (y/N): ")
        if train_confirm.lower() == 'y':
            if train_demo_model():
                # Inference
                run_demo_inference()
                
                # Benchmark
                run_demo_benchmark()
            else:
                print("Training failed. Skipping inference and benchmark.")
        else:
            print("Skipping training. You can run inference if you have a pre-trained model.")
        
        print("🎉 Demo pipeline completed!")
    
    elif args.setup:
        setup_demo_environment()
    
    elif args.train:
        train_demo_model()
    
    elif args.inference:
        run_demo_inference()
    
    elif args.benchmark:
        run_demo_benchmark()
    
    elif args.ui:
        print("Launching Streamlit UI...")
        print("Run: streamlit run src/ui/streamlit_app.py")
        os.system("streamlit run src/ui/streamlit_app.py")
    
    else:
        print("Image Sharpening with Knowledge Distillation Demo")
        print("=" * 50)
        print("Available options:")
        print("  --setup      Set up demo environment with sample data")
        print("  --train      Train a demo model")
        print("  --inference  Run inference on sample images")
        print("  --benchmark  Run performance benchmark")
        print("  --ui         Launch Streamlit web interface")
        print("  --all        Run complete demo pipeline")
        print()
        print("Quick start:")
        print("  python demo.py --setup")
        print("  python demo.py --train")
        print("  python demo.py --inference")
        print()
        print("Or run everything:")
        print("  python demo.py --all")


if __name__ == "__main__":
    main()
