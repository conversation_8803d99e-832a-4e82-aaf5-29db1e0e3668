"""
Streamlit Web Interface for Image Sharpening

This module provides a user-friendly web interface for image sharpening
with webcam capture, manual upload, and result comparison features.
"""

import streamlit as st
import cv2
import numpy as np
from PIL import Image
import torch
import time
import io
import base64
from pathlib import Path
import yaml
import matplotlib.pyplot as plt

# Add parent directory to path for imports
import sys
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.inference.image_processor import ImageProcessor
from src.evaluation.metrics import MetricCalculator


class StreamlitImageSharpener:
    """Streamlit web interface for image sharpening."""
    
    def __init__(self):
        """Initialize the Streamlit app."""
        st.set_page_config(
            page_title="Real-time Image Sharpening",
            page_icon="🔍",
            layout="wide",
            initial_sidebar_state="expanded"
        )
        
        self.processor = None
        self.metrics_calculator = None
        
        # Initialize session state
        if 'processed_image' not in st.session_state:
            st.session_state.processed_image = None
        if 'original_image' not in st.session_state:
            st.session_state.original_image = None
        if 'metrics' not in st.session_state:
            st.session_state.metrics = None
    
    def load_model(self, model_path: str, config_path: str = "configs/config.yaml"):
        """Load the image sharpening model."""
        try:
            self.processor = ImageProcessor(model_path, config_path)
            self.metrics_calculator = MetricCalculator()
            return True
        except Exception as e:
            st.error(f"Error loading model: {e}")
            return False
    
    def process_image(self, image: np.ndarray) -> tuple:
        """Process an image and return enhanced version with metrics."""
        if self.processor is None:
            return None, None
        
        try:
            # Convert PIL to OpenCV format if needed
            if len(image.shape) == 3 and image.shape[2] == 3:
                # RGB to BGR
                image_bgr = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
            else:
                image_bgr = image
            
            # Process image
            enhanced_image = self.processor.process_image(
                image_bgr if isinstance(image_bgr, str) else None,
                return_metrics=False
            )
            
            # Convert back to RGB for display
            enhanced_rgb = cv2.cvtColor(enhanced_image, cv2.COLOR_BGR2RGB)
            
            # Calculate metrics (comparing with original)
            metrics = None
            if self.metrics_calculator:
                try:
                    # Convert images to tensors for metrics
                    original_tensor = torch.from_numpy(image).float().permute(2, 0, 1).unsqueeze(0) / 255.0
                    enhanced_tensor = torch.from_numpy(enhanced_rgb).float().permute(2, 0, 1).unsqueeze(0) / 255.0
                    
                    metrics = self.metrics_calculator.calculate_all_metrics(
                        enhanced_tensor, original_tensor
                    )
                except Exception as e:
                    st.warning(f"Could not calculate metrics: {e}")
            
            return enhanced_rgb, metrics
            
        except Exception as e:
            st.error(f"Error processing image: {e}")
            return None, None
    
    def create_comparison_image(self, original: np.ndarray, enhanced: np.ndarray) -> np.ndarray:
        """Create side-by-side comparison image."""
        # Ensure both images have the same height
        h1, w1 = original.shape[:2]
        h2, w2 = enhanced.shape[:2]
        
        if h1 != h2:
            # Resize to match heights
            target_height = min(h1, h2)
            original = cv2.resize(original, (int(w1 * target_height / h1), target_height))
            enhanced = cv2.resize(enhanced, (int(w2 * target_height / h2), target_height))
        
        # Create side-by-side comparison
        comparison = np.hstack([original, enhanced])
        
        return comparison
    
    def display_metrics(self, metrics: dict):
        """Display quality metrics in a formatted way."""
        if not metrics:
            return
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("SSIM", f"{metrics.get('ssim', 0):.4f}")
            st.metric("PSNR", f"{metrics.get('psnr', 0):.2f} dB")
        
        with col2:
            st.metric("LPIPS", f"{metrics.get('lpips', 0):.4f}")
            st.metric("MSE", f"{metrics.get('mse', 0):.6f}")
        
        with col3:
            st.metric("MAE", f"{metrics.get('mae', 0):.6f}")
            st.metric("Edge Preservation", f"{metrics.get('edge_preservation', 0):.4f}")
    
    def run(self):
        """Run the Streamlit application."""
        st.title("🔍 Real-time Image Sharpening")
        st.markdown("Enhance your images using AI-powered knowledge distillation")
        
        # Sidebar for model configuration
        with st.sidebar:
            st.header("Configuration")
            
            # Model selection
            model_path = st.text_input(
                "Model Path",
                value="models/student/best_model.pth",
                help="Path to the trained student model"
            )
            
            config_path = st.text_input(
                "Config Path",
                value="configs/config.yaml",
                help="Path to the configuration file"
            )
            
            # Load model button
            if st.button("Load Model"):
                with st.spinner("Loading model..."):
                    if self.load_model(model_path, config_path):
                        st.success("Model loaded successfully!")
                    else:
                        st.error("Failed to load model")
            
            # Model status
            if self.processor is not None:
                st.success("✅ Model Ready")
            else:
                st.warning("⚠️ Model Not Loaded")
        
        # Main interface tabs
        tab1, tab2, tab3 = st.tabs(["📁 Upload Image", "📷 Webcam Capture", "📊 Batch Processing"])
        
        with tab1:
            self.upload_interface()
        
        with tab2:
            self.webcam_interface()
        
        with tab3:
            self.batch_interface()
    
    def upload_interface(self):
        """Interface for uploading and processing single images."""
        st.header("Upload and Process Image")
        
        uploaded_file = st.file_uploader(
            "Choose an image file",
            type=['png', 'jpg', 'jpeg', 'bmp', 'tiff'],
            help="Upload an image to enhance"
        )
        
        if uploaded_file is not None:
            # Load and display original image
            image = Image.open(uploaded_file)
            image_array = np.array(image)
            
            st.session_state.original_image = image_array
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.subheader("Original Image")
                st.image(image_array, use_column_width=True)
                st.write(f"Size: {image_array.shape[1]}x{image_array.shape[0]}")
            
            # Process button
            if st.button("🚀 Enhance Image", disabled=(self.processor is None)):
                with st.spinner("Processing image..."):
                    start_time = time.time()
                    enhanced_image, metrics = self.process_image(image_array)
                    processing_time = time.time() - start_time
                    
                    if enhanced_image is not None:
                        st.session_state.processed_image = enhanced_image
                        st.session_state.metrics = metrics
                        
                        with col2:
                            st.subheader("Enhanced Image")
                            st.image(enhanced_image, use_column_width=True)
                            st.write(f"Processing time: {processing_time:.2f}s")
            
            # Display processed image if available
            if st.session_state.processed_image is not None:
                with col2:
                    if col2.empty():  # Only show if not already shown
                        st.subheader("Enhanced Image")
                        st.image(st.session_state.processed_image, use_column_width=True)
                
                # Metrics display
                if st.session_state.metrics:
                    st.subheader("Quality Metrics")
                    self.display_metrics(st.session_state.metrics)
                
                # Comparison view
                st.subheader("Side-by-Side Comparison")
                comparison = self.create_comparison_image(
                    st.session_state.original_image,
                    st.session_state.processed_image
                )
                st.image(comparison, use_column_width=True, caption="Original (Left) vs Enhanced (Right)")
                
                # Download button
                if st.button("💾 Download Enhanced Image"):
                    # Convert to PIL Image for download
                    pil_image = Image.fromarray(st.session_state.processed_image)
                    
                    # Create download buffer
                    buf = io.BytesIO()
                    pil_image.save(buf, format='PNG')
                    byte_im = buf.getvalue()
                    
                    st.download_button(
                        label="Download PNG",
                        data=byte_im,
                        file_name="enhanced_image.png",
                        mime="image/png"
                    )
    
    def webcam_interface(self):
        """Interface for webcam capture and processing."""
        st.header("Webcam Capture")
        st.info("📷 Use your webcam to capture and enhance images in real-time")
        
        # Webcam capture
        camera_input = st.camera_input("Take a picture")
        
        if camera_input is not None:
            # Load captured image
            image = Image.open(camera_input)
            image_array = np.array(image)
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.subheader("Captured Image")
                st.image(image_array, use_column_width=True)
            
            # Auto-process if model is loaded
            if self.processor is not None:
                with st.spinner("Enhancing captured image..."):
                    enhanced_image, metrics = self.process_image(image_array)
                    
                    if enhanced_image is not None:
                        with col2:
                            st.subheader("Enhanced Image")
                            st.image(enhanced_image, use_column_width=True)
                        
                        # Show metrics
                        if metrics:
                            st.subheader("Quality Metrics")
                            self.display_metrics(metrics)
            else:
                st.warning("Please load a model first to process the captured image")
    
    def batch_interface(self):
        """Interface for batch processing multiple images."""
        st.header("Batch Processing")
        st.info("📁 Upload multiple images for batch processing")
        
        uploaded_files = st.file_uploader(
            "Choose image files",
            type=['png', 'jpg', 'jpeg', 'bmp', 'tiff'],
            accept_multiple_files=True,
            help="Upload multiple images to process in batch"
        )
        
        if uploaded_files and self.processor is not None:
            if st.button("🚀 Process All Images"):
                progress_bar = st.progress(0)
                results = []
                
                for i, uploaded_file in enumerate(uploaded_files):
                    # Update progress
                    progress_bar.progress((i + 1) / len(uploaded_files))
                    
                    # Process image
                    image = Image.open(uploaded_file)
                    image_array = np.array(image)
                    
                    enhanced_image, metrics = self.process_image(image_array)
                    
                    if enhanced_image is not None:
                        results.append({
                            'name': uploaded_file.name,
                            'original': image_array,
                            'enhanced': enhanced_image,
                            'metrics': metrics
                        })
                
                # Display results
                st.subheader(f"Processed {len(results)} images")
                
                for result in results:
                    with st.expander(f"📸 {result['name']}"):
                        col1, col2 = st.columns(2)
                        
                        with col1:
                            st.write("Original")
                            st.image(result['original'], use_column_width=True)
                        
                        with col2:
                            st.write("Enhanced")
                            st.image(result['enhanced'], use_column_width=True)
                        
                        if result['metrics']:
                            self.display_metrics(result['metrics'])
        
        elif uploaded_files and self.processor is None:
            st.warning("Please load a model first to process the images")


def main():
    """Main function to run the Streamlit app."""
    app = StreamlitImageSharpener()
    app.run()


if __name__ == "__main__":
    main()
