"""
Setup script for Image Sharpening with Knowledge Distillation
"""

from setuptools import setup, find_packages
import os

# Read README file
def read_readme():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# Read requirements
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as fh:
        return [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="image-sharpening-kd",
    version="1.0.0",
    author="Image Sharpening Team",
    author_email="<EMAIL>",
    description="Real-time image sharpening for video conferencing using knowledge distillation",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/image-sharpening-kd",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Scientific/Engineering :: Image Processing",
        "Topic :: Multimedia :: Graphics :: Graphics Conversion",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.4.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "jupyter>=1.0.0",
            "notebook>=6.5.0",
        ],
        "gpu": [
            "torch>=2.0.0+cu118",
            "torchvision>=0.15.0+cu118",
        ],
        "deployment": [
            "onnx>=1.14.0",
            "onnxruntime-gpu>=1.15.0",
            "tensorrt>=8.6.0",
            "openvino>=2023.0.0",
        ]
    },
    entry_points={
        "console_scripts": [
            "image-sharpening-demo=demo:main",
            "image-sharpening-train=src.training.distillation_trainer:main",
            "image-sharpening-inference=src.inference.realtime_sharpening:main",
            "image-sharpening-benchmark=src.evaluation.benchmark:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["configs/*.yaml", "*.md", "*.txt"],
    },
    zip_safe=False,
)
