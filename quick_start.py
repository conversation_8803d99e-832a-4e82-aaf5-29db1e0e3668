#!/usr/bin/env python3
"""
Quick Start Script for Image Sharpening with Knowledge Distillation

This script provides a simple way to get started with the image sharpening system.
It handles environment setup, sample data generation, and basic usage examples.
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python version: {version.major}.{version.minor}.{version.micro}")
    return True


def check_dependencies():
    """Check if required dependencies are installed."""
    required_packages = [
        'torch', 'torchvision', 'opencv-python', 'numpy', 
        'pillow', 'matplotlib', 'tqdm', 'pyyaml'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    return missing_packages


def install_dependencies(missing_packages):
    """Install missing dependencies."""
    if not missing_packages:
        return True
    
    print(f"\n📦 Installing missing packages: {', '.join(missing_packages)}")
    
    try:
        # Try installing from requirements.txt first
        if Path("requirements.txt").exists():
            subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        else:
            # Install individual packages
            subprocess.check_call([sys.executable, "-m", "pip", "install"] + missing_packages)
        
        print("✅ Dependencies installed successfully")
        return True
    
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False


def setup_directories():
    """Create necessary directories."""
    directories = [
        "datasets/raw",
        "datasets/processed", 
        "models/teacher",
        "models/student",
        "results",
        "logs"
    ]
    
    print("\n📁 Setting up directories...")
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ {directory}")


def generate_sample_data():
    """Generate sample data for testing."""
    print("\n🎨 Generating sample data...")
    
    try:
        # Import and run sample data generator
        sys.path.append('src')
        from src.data.download_sample_data import SampleDataDownloader
        
        downloader = SampleDataDownloader("datasets/raw")
        downloader.create_sample_dataset()
        
        print("✅ Sample data generated successfully")
        return True
    
    except Exception as e:
        print(f"❌ Failed to generate sample data: {e}")
        return False


def show_usage_examples():
    """Show usage examples."""
    print("\n" + "="*60)
    print("🚀 QUICK START GUIDE")
    print("="*60)
    
    print("\n1. Generate and prepare training data:")
    print("   python src/data/download_sample_data.py")
    print("   python src/data/dataset_generator.py --input_dir datasets/raw --output_dir datasets/processed")
    
    print("\n2. Train the model:")
    print("   python src/training/distillation_trainer.py")
    
    print("\n3. Run real-time inference:")
    print("   python src/inference/realtime_sharpening.py --model models/student/best_model.pth")
    
    print("\n4. Process single image:")
    print("   python src/inference/image_processor.py --model models/student/best_model.pth --input image.jpg --output enhanced.jpg")
    
    print("\n5. Run benchmark:")
    print("   python src/evaluation/benchmark.py --model models/student/best_model.pth --test_dir datasets/raw")
    
    print("\n6. Launch web interface:")
    print("   streamlit run src/ui/streamlit_app.py")
    
    print("\n7. Run complete demo:")
    print("   python demo.py --all")
    
    print("\n" + "="*60)
    print("📚 For more information, see README.md")
    print("🐛 For issues, check the documentation or create an issue")
    print("="*60)


def main():
    """Main quick start function."""
    print("🔍 Image Sharpening with Knowledge Distillation - Quick Start")
    print("="*60)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Check dependencies
    print("\n📋 Checking dependencies...")
    missing_packages = check_dependencies()
    
    if missing_packages:
        install_choice = input(f"\n❓ Install missing packages? (y/N): ")
        if install_choice.lower() == 'y':
            if not install_dependencies(missing_packages):
                print("❌ Failed to install dependencies. Please install manually.")
                sys.exit(1)
        else:
            print("⚠️  Some features may not work without required dependencies")
    
    # Setup directories
    setup_directories()
    
    # Generate sample data
    generate_choice = input("\n❓ Generate sample data for testing? (Y/n): ")
    if generate_choice.lower() != 'n':
        generate_sample_data()
    
    # Show usage examples
    show_usage_examples()
    
    # Ask what to do next
    print("\n❓ What would you like to do next?")
    print("1. Run complete demo (includes training)")
    print("2. Generate sample data only")
    print("3. Launch web interface")
    print("4. Show usage examples again")
    print("5. Exit")
    
    choice = input("\nEnter choice (1-5): ").strip()
    
    if choice == '1':
        print("\n🚀 Running complete demo...")
        os.system("python demo.py --all")
    
    elif choice == '2':
        print("\n🎨 Generating sample data...")
        os.system("python demo.py --setup")
    
    elif choice == '3':
        print("\n🌐 Launching web interface...")
        print("Opening Streamlit app in your browser...")
        os.system("streamlit run src/ui/streamlit_app.py")
    
    elif choice == '4':
        show_usage_examples()
    
    elif choice == '5':
        print("\n👋 Thanks for using Image Sharpening with Knowledge Distillation!")
    
    else:
        print("\n❓ Invalid choice. Run 'python quick_start.py' again to restart.")


if __name__ == "__main__":
    main()
