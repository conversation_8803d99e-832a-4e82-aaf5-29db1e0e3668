# Image Sharpening with Knowledge Distillation Configuration

# Project Settings
project:
  name: "image_sharpening_kd"
  version: "1.0.0"
  description: "Real-time image sharpening for video conferencing using knowledge distillation"

# Data Configuration
data:
  input_resolution: [480, 640]  # Training input size (height, width)
  target_resolution: [1080, 1920]  # Target output size (height, width)
  crop_size: [256, 256]  # Crop size for training patches
  batch_size: 16
  num_workers: 4
  
  # Data augmentation
  augmentation:
    horizontal_flip: 0.5
    vertical_flip: 0.0
    rotation: 10
    brightness: 0.1
    contrast: 0.1
    saturation: 0.1
    hue: 0.05

# Model Configuration
model:
  # Teacher model settings
  teacher:
    name: "ESRGAN"  # or "RealESRGAN", "EDSR", "SwinIR"
    pretrained: true
    scale_factor: 4
    
  # Student model settings
  student:
    name: "LightweightSR"
    channels: 32
    num_blocks: 4
    scale_factor: 4
    activation: "relu"
    use_attention: true
    
# Training Configuration
training:
  epochs: 100
  learning_rate: 0.0002
  weight_decay: 0.0001
  scheduler: "cosine"
  warmup_epochs: 5
  
  # Loss weights
  loss:
    mse_weight: 1.0
    perceptual_weight: 0.1
    adversarial_weight: 0.01
    distillation_weight: 0.5
    ssim_weight: 0.1
    
  # Knowledge distillation
  distillation:
    temperature: 4.0
    alpha: 0.7  # Weight for distillation loss
    beta: 0.3   # Weight for ground truth loss

# Evaluation Configuration
evaluation:
  metrics: ["ssim", "psnr", "lpips", "mse"]
  target_ssim: 0.90
  target_fps: 30
  benchmark_images: 100
  
# Inference Configuration
inference:
  device: "cuda"  # or "cpu"
  batch_size: 1
  use_half_precision: true
  optimize_for_mobile: false
  
  # Model optimization
  optimization:
    use_onnx: true
    use_tensorrt: false
    use_openvino: false
    quantization: "int8"  # or "fp16", "fp32"

# Hardware Configuration
hardware:
  gpu_memory_fraction: 0.8
  allow_growth: true
  mixed_precision: true

# Logging and Monitoring
logging:
  use_wandb: true
  use_tensorboard: true
  log_interval: 100
  save_interval: 1000
  
# Paths
paths:
  datasets: "./datasets"
  models: "./models"
  results: "./results"
  logs: "./logs"
