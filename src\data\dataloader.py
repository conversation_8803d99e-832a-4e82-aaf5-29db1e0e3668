"""
Data Loader for Image Sharpening Training

Provides PyTorch DataLoader for training the knowledge distillation model.
"""

import os
import cv2
import torch
import numpy as np
from torch.utils.data import Dataset, DataLoader
from torchvision import transforms
from PIL import Image
from pathlib import Path
from typing import Tuple, Optional, List
import yaml


class ImageSharpeningDataset(Dataset):
    """
    Dataset class for image sharpening training pairs.
    """
    
    def __init__(
        self, 
        dataset_dir: str,
        transform: Optional[transforms.Compose] = None,
        target_transform: Optional[transforms.Compose] = None
    ):
        """
        Initialize the dataset.
        
        Args:
            dataset_dir: Directory containing degraded and ground_truth subdirectories
            transform: Transforms to apply to input (degraded) images
            target_transform: Transforms to apply to target (ground truth) images
        """
        self.dataset_dir = dataset_dir
        self.degraded_dir = os.path.join(dataset_dir, "degraded")
        self.gt_dir = os.path.join(dataset_dir, "ground_truth")
        
        self.transform = transform
        self.target_transform = target_transform
        
        # Get list of image pairs
        self.image_pairs = self._get_image_pairs()
        
        print(f"Loaded {len(self.image_pairs)} image pairs from {dataset_dir}")
    
    def _get_image_pairs(self) -> List[Tuple[str, str]]:
        """Get list of (degraded, ground_truth) image pairs."""
        pairs = []
        
        # Get all degraded images
        degraded_files = list(Path(self.degraded_dir).glob("*.png"))
        
        for deg_file in degraded_files:
            # Find corresponding ground truth file
            base_name = deg_file.stem.replace("_deg_", "_gt_")
            gt_file = Path(self.gt_dir) / f"{base_name}.png"
            
            if gt_file.exists():
                pairs.append((str(deg_file), str(gt_file)))
        
        return pairs
    
    def __len__(self) -> int:
        return len(self.image_pairs)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Get a training sample.
        
        Args:
            idx: Sample index
            
        Returns:
            Tuple of (degraded_image, ground_truth_image) as tensors
        """
        deg_path, gt_path = self.image_pairs[idx]
        
        # Load images
        degraded = cv2.imread(deg_path)
        ground_truth = cv2.imread(gt_path)
        
        # Convert BGR to RGB
        degraded = cv2.cvtColor(degraded, cv2.COLOR_BGR2RGB)
        ground_truth = cv2.cvtColor(ground_truth, cv2.COLOR_BGR2RGB)
        
        # Convert to PIL Images for transforms
        degraded = Image.fromarray(degraded)
        ground_truth = Image.fromarray(ground_truth)
        
        # Apply transforms
        if self.transform:
            degraded = self.transform(degraded)
        else:
            degraded = transforms.ToTensor()(degraded)
            
        if self.target_transform:
            ground_truth = self.target_transform(ground_truth)
        else:
            ground_truth = transforms.ToTensor()(ground_truth)
        
        return degraded, ground_truth


def get_transforms(config: dict) -> Tuple[transforms.Compose, transforms.Compose]:
    """
    Get data transforms for training.
    
    Args:
        config: Configuration dictionary
        
    Returns:
        Tuple of (input_transform, target_transform)
    """
    # Input transforms (for degraded images)
    input_transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    # Target transforms (for ground truth images)
    target_transform = transforms.Compose([
        transforms.ToTensor(),
    ])
    
    return input_transform, target_transform


def create_dataloaders(
    config_path: str = "configs/config.yaml",
    train_dir: str = "datasets/processed/train",
    val_dir: str = "datasets/processed/val"
) -> Tuple[DataLoader, DataLoader]:
    """
    Create training and validation data loaders.
    
    Args:
        config_path: Path to configuration file
        train_dir: Training dataset directory
        val_dir: Validation dataset directory
        
    Returns:
        Tuple of (train_loader, val_loader)
    """
    # Load configuration
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    # Get transforms
    input_transform, target_transform = get_transforms(config)
    
    # Create datasets
    train_dataset = ImageSharpeningDataset(
        train_dir,
        transform=input_transform,
        target_transform=target_transform
    )
    
    val_dataset = ImageSharpeningDataset(
        val_dir,
        transform=input_transform,
        target_transform=target_transform
    )
    
    # Create data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=config['data']['batch_size'],
        shuffle=True,
        num_workers=config['data']['num_workers'],
        pin_memory=True,
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config['data']['batch_size'],
        shuffle=False,
        num_workers=config['data']['num_workers'],
        pin_memory=True,
        drop_last=False
    )
    
    return train_loader, val_loader


class WebcamDataset:
    """
    Real-time dataset for webcam input processing.
    """
    
    def __init__(self, camera_id: int = 0, target_size: Tuple[int, int] = (1080, 1920)):
        """
        Initialize webcam dataset.
        
        Args:
            camera_id: Camera device ID
            target_size: Target output size (height, width)
        """
        self.camera_id = camera_id
        self.target_size = target_size
        self.cap = None
        
        # Preprocessing transform
        self.transform = transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
    
    def start_capture(self) -> bool:
        """Start video capture."""
        self.cap = cv2.VideoCapture(self.camera_id)
        if not self.cap.isOpened():
            print(f"Error: Could not open camera {self.camera_id}")
            return False
        
        # Set camera properties for better quality
        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1920)
        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 1080)
        self.cap.set(cv2.CAP_PROP_FPS, 30)
        
        return True
    
    def get_frame(self) -> Optional[Tuple[torch.Tensor, np.ndarray]]:
        """
        Get next frame from webcam.
        
        Returns:
            Tuple of (preprocessed_tensor, original_frame) or None if failed
        """
        if self.cap is None:
            return None
        
        ret, frame = self.cap.read()
        if not ret:
            return None
        
        # Convert BGR to RGB
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        
        # Resize to target size if needed
        h, w = frame_rgb.shape[:2]
        if (h, w) != self.target_size:
            frame_rgb = cv2.resize(frame_rgb, (self.target_size[1], self.target_size[0]))
        
        # Convert to PIL and apply transform
        pil_image = Image.fromarray(frame_rgb)
        tensor = self.transform(pil_image).unsqueeze(0)  # Add batch dimension
        
        return tensor, frame_rgb
    
    def stop_capture(self):
        """Stop video capture."""
        if self.cap is not None:
            self.cap.release()
            self.cap = None


if __name__ == "__main__":
    # Test the data loader
    train_loader, val_loader = create_dataloaders()
    
    print(f"Training batches: {len(train_loader)}")
    print(f"Validation batches: {len(val_loader)}")
    
    # Test a batch
    for batch_idx, (degraded, ground_truth) in enumerate(train_loader):
        print(f"Batch {batch_idx}: Degraded shape: {degraded.shape}, GT shape: {ground_truth.shape}")
        if batch_idx >= 2:  # Only test first few batches
            break
