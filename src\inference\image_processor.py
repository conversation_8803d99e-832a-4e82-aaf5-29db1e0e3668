"""
Image Processor for Batch Processing

This module provides a simple interface for processing individual images
or batches of images using the trained student model.
"""

import cv2
import torch
import numpy as np
from PIL import Image
import yaml
from pathlib import Path
from typing import Union, List, Optional, Tuple
import argparse
import time
from tqdm import tqdm

from ..models.student_models import StudentModelFactory
from ..evaluation.metrics import MetricCalculator


class ImageProcessor:
    """
    Image processor for enhancing images using trained student model.
    
    Provides simple interface for single image or batch processing.
    """
    
    def __init__(
        self,
        model_path: str,
        config_path: str = "configs/config.yaml",
        device: Optional[str] = None
    ):
        """
        Initialize the image processor.
        
        Args:
            model_path: Path to trained student model
            config_path: Path to configuration file
            device: Device to use ('cuda', 'cpu', or None for auto)
        """
        self.model_path = model_path
        
        # Load configuration
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        # Set device
        if device is None:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
        
        print(f"Using device: {self.device}")
        
        # Load model
        self.model = self._load_model()
        
        # Initialize metrics calculator
        self.metrics_calculator = MetricCalculator(str(self.device))
    
    def _load_model(self) -> torch.nn.Module:
        """Load the trained student model."""
        # Create model architecture
        model = StudentModelFactory.create_student(
            self.config['model']['student']['name'],
            scale_factor=self.config['model']['student']['scale_factor'],
            base_channels=self.config['model']['student']['channels'],
            num_blocks=self.config['model']['student']['num_blocks'],
            use_attention=self.config['model']['student']['use_attention']
        )
        
        # Load trained weights
        if Path(self.model_path).exists():
            checkpoint = torch.load(self.model_path, map_location=self.device)
            if 'student_state_dict' in checkpoint:
                model.load_state_dict(checkpoint['student_state_dict'])
            else:
                model.load_state_dict(checkpoint)
            print(f"Loaded model from {self.model_path}")
        else:
            print(f"Warning: Model file {self.model_path} not found. Using random weights.")
        
        model.to(self.device)
        model.eval()
        
        return model
    
    def _preprocess_image(self, image: np.ndarray) -> torch.Tensor:
        """
        Preprocess image for model input.
        
        Args:
            image: Input image as numpy array (BGR format)
            
        Returns:
            Preprocessed tensor ready for model input
        """
        # Convert BGR to RGB
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Convert to tensor and normalize to [0, 1]
        tensor = torch.from_numpy(image_rgb).float() / 255.0
        
        # Rearrange dimensions: (H, W, C) -> (1, C, H, W)
        tensor = tensor.permute(2, 0, 1).unsqueeze(0)
        
        return tensor.to(self.device)
    
    def _postprocess_output(self, tensor: torch.Tensor) -> np.ndarray:
        """
        Postprocess model output to displayable image.
        
        Args:
            tensor: Model output tensor
            
        Returns:
            Postprocessed image as numpy array (BGR format)
        """
        # Clamp values to [0, 1]
        tensor = torch.clamp(tensor, 0, 1)
        
        # Convert to numpy: (1, C, H, W) -> (H, W, C)
        image = tensor.squeeze(0).permute(1, 2, 0).cpu().numpy()
        
        # Convert to uint8
        image = (image * 255).astype(np.uint8)
        
        # Convert RGB to BGR for OpenCV
        image_bgr = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
        
        return image_bgr
    
    def process_image(
        self,
        input_path: str,
        output_path: Optional[str] = None,
        return_metrics: bool = False,
        ground_truth_path: Optional[str] = None
    ) -> Union[np.ndarray, Tuple[np.ndarray, dict]]:
        """
        Process a single image.
        
        Args:
            input_path: Path to input image
            output_path: Path to save enhanced image (optional)
            return_metrics: Whether to return quality metrics
            ground_truth_path: Path to ground truth image for metrics calculation
            
        Returns:
            Enhanced image array, optionally with metrics dictionary
        """
        # Load input image
        input_image = cv2.imread(input_path)
        if input_image is None:
            raise ValueError(f"Could not load image: {input_path}")
        
        print(f"Processing: {input_path}")
        print(f"Input size: {input_image.shape}")
        
        # Preprocess
        input_tensor = self._preprocess_image(input_image)
        
        # Inference
        start_time = time.time()
        with torch.no_grad():
            output_tensor = self.model(input_tensor)
        inference_time = time.time() - start_time
        
        # Postprocess
        enhanced_image = self._postprocess_output(output_tensor)
        
        print(f"Output size: {enhanced_image.shape}")
        print(f"Inference time: {inference_time*1000:.2f}ms")
        
        # Save output if path provided
        if output_path:
            cv2.imwrite(output_path, enhanced_image)
            print(f"Saved enhanced image: {output_path}")
        
        # Calculate metrics if requested
        metrics = {}
        if return_metrics and ground_truth_path:
            ground_truth = cv2.imread(ground_truth_path)
            if ground_truth is not None:
                # Convert to tensors for metrics calculation
                enhanced_tensor = self._preprocess_image(enhanced_image)
                gt_tensor = self._preprocess_image(ground_truth)
                
                metrics = self.metrics_calculator.calculate_all_metrics(
                    enhanced_tensor, gt_tensor
                )
                
                print("Quality Metrics:")
                for name, value in metrics.items():
                    print(f"  {name.upper()}: {value:.4f}")
        
        if return_metrics:
            return enhanced_image, metrics
        else:
            return enhanced_image
    
    def process_batch(
        self,
        input_dir: str,
        output_dir: str,
        file_extensions: List[str] = ['.jpg', '.jpeg', '.png', '.bmp'],
        ground_truth_dir: Optional[str] = None
    ) -> dict:
        """
        Process a batch of images.
        
        Args:
            input_dir: Directory containing input images
            output_dir: Directory to save enhanced images
            file_extensions: List of file extensions to process
            ground_truth_dir: Directory with ground truth images for metrics
            
        Returns:
            Dictionary with processing statistics and metrics
        """
        input_path = Path(input_dir)
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Find all images
        image_files = []
        for ext in file_extensions:
            image_files.extend(input_path.glob(f"*{ext}"))
            image_files.extend(input_path.glob(f"*{ext.upper()}"))
        
        if not image_files:
            print(f"No images found in {input_dir}")
            return {}
        
        print(f"Found {len(image_files)} images to process")
        
        # Process images
        total_time = 0
        all_metrics = []
        
        for image_file in tqdm(image_files, desc="Processing images"):
            try:
                # Set output path
                output_file = output_path / f"{image_file.stem}_enhanced{image_file.suffix}"
                
                # Set ground truth path if available
                gt_path = None
                if ground_truth_dir:
                    gt_file = Path(ground_truth_dir) / image_file.name
                    if gt_file.exists():
                        gt_path = str(gt_file)
                
                # Process image
                start_time = time.time()
                if gt_path:
                    _, metrics = self.process_image(
                        str(image_file),
                        str(output_file),
                        return_metrics=True,
                        ground_truth_path=gt_path
                    )
                    all_metrics.append(metrics)
                else:
                    self.process_image(str(image_file), str(output_file))
                
                total_time += time.time() - start_time
                
            except Exception as e:
                print(f"Error processing {image_file}: {e}")
        
        # Calculate average metrics
        avg_metrics = {}
        if all_metrics:
            for key in all_metrics[0].keys():
                avg_metrics[f"avg_{key}"] = np.mean([m[key] for m in all_metrics])
        
        # Prepare statistics
        stats = {
            'total_images': len(image_files),
            'total_time': total_time,
            'avg_time_per_image': total_time / len(image_files),
            'fps': len(image_files) / total_time,
            **avg_metrics
        }
        
        print(f"\nBatch Processing Complete:")
        print(f"  Total images: {stats['total_images']}")
        print(f"  Total time: {stats['total_time']:.2f}s")
        print(f"  Average time per image: {stats['avg_time_per_image']*1000:.2f}ms")
        print(f"  Processing FPS: {stats['fps']:.2f}")
        
        if avg_metrics:
            print(f"  Average Quality Metrics:")
            for name, value in avg_metrics.items():
                print(f"    {name}: {value:.4f}")
        
        return stats
    
    def compare_with_ground_truth(
        self,
        input_path: str,
        ground_truth_path: str,
        output_path: Optional[str] = None
    ) -> dict:
        """
        Process image and compare with ground truth.
        
        Args:
            input_path: Path to input (degraded) image
            ground_truth_path: Path to ground truth image
            output_path: Path to save comparison image
            
        Returns:
            Dictionary with quality metrics
        """
        # Process image
        enhanced_image, metrics = self.process_image(
            input_path,
            return_metrics=True,
            ground_truth_path=ground_truth_path
        )
        
        # Create comparison image if output path provided
        if output_path:
            input_image = cv2.imread(input_path)
            gt_image = cv2.imread(ground_truth_path)
            
            # Resize images to same size for comparison
            h, w = enhanced_image.shape[:2]
            input_resized = cv2.resize(input_image, (w, h))
            gt_resized = cv2.resize(gt_image, (w, h))
            
            # Create side-by-side comparison
            comparison = np.hstack([input_resized, enhanced_image, gt_resized])
            
            # Add labels
            font = cv2.FONT_HERSHEY_SIMPLEX
            cv2.putText(comparison, "Input", (10, 30), font, 1, (0, 255, 0), 2)
            cv2.putText(comparison, "Enhanced", (w + 10, 30), font, 1, (0, 255, 0), 2)
            cv2.putText(comparison, "Ground Truth", (2*w + 10, 30), font, 1, (0, 255, 0), 2)
            
            cv2.imwrite(output_path, comparison)
            print(f"Comparison saved: {output_path}")
        
        return metrics


def main():
    """Main function for image processing."""
    parser = argparse.ArgumentParser(description="Image Processing with Trained Model")
    parser.add_argument("--model", type=str, required=True, help="Path to trained model")
    parser.add_argument("--config", type=str, default="configs/config.yaml", help="Config file")
    parser.add_argument("--input", type=str, required=True, help="Input image or directory")
    parser.add_argument("--output", type=str, help="Output image or directory")
    parser.add_argument("--ground_truth", type=str, help="Ground truth image or directory")
    parser.add_argument("--batch", action="store_true", help="Process batch of images")
    parser.add_argument("--compare", action="store_true", help="Create comparison image")
    
    args = parser.parse_args()
    
    # Create processor
    processor = ImageProcessor(args.model, args.config)
    
    if args.batch:
        # Batch processing
        output_dir = args.output or f"{args.input}_enhanced"
        stats = processor.process_batch(
            args.input,
            output_dir,
            ground_truth_dir=args.ground_truth
        )
    elif args.compare and args.ground_truth:
        # Comparison mode
        output_path = args.output or args.input.replace('.', '_comparison.')
        metrics = processor.compare_with_ground_truth(
            args.input,
            args.ground_truth,
            output_path
        )
    else:
        # Single image processing
        output_path = args.output or args.input.replace('.', '_enhanced.')
        processor.process_image(
            args.input,
            output_path,
            return_metrics=bool(args.ground_truth),
            ground_truth_path=args.ground_truth
        )


if __name__ == "__main__":
    main()
