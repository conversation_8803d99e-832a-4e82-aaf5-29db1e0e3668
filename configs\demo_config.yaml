data:
  augmentation:
    brightness: 0.1
    contrast: 0.1
    horizontal_flip: 0.5
    hue: 0.05
    rotation: 10
    saturation: 0.1
    vertical_flip: 0.0
  batch_size: 4
  crop_size:
  - 128
  - 128
  input_resolution:
  - 240
  - 320
  num_workers: 2
  target_resolution:
  - 480
  - 640
evaluation:
  metrics:
  - ssim
  - psnr
  - mse
  target_fps: 30
  target_ssim: 0.85
inference:
  batch_size: 1
  device: cuda
  optimize_for_mobile: false
  use_half_precision: false
logging:
  log_interval: 10
  save_interval: 2
  use_tensorboard: true
  use_wandb: false
model:
  student:
    channels: 16
    name: tiny
    num_blocks: 3
    scale_factor: 2
    use_attention: false
  teacher:
    name: edsr
    scale_factor: 2
paths:
  datasets: ./datasets
  logs: ./logs
  models: ./models
  results: ./results
project:
  name: image_sharpening_demo
  version: 1.0.0
training:
  distillation:
    alpha: 0.7
    beta: 0.3
    temperature: 4.0
  epochs: 5
  learning_rate: 0.001
  loss:
    adversarial_weight: 0.0
    distillation_weight: 0.5
    mse_weight: 1.0
    perceptual_weight: 0.1
    ssim_weight: 0.1
  scheduler: cosine
  warmup_epochs: 1
  weight_decay: 0.0001
