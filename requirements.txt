# Core Deep Learning and Computer Vision
torch>=2.0.0
torchvision>=0.15.0
opencv-python>=4.8.0
Pillow>=10.0.0
numpy>=1.24.0
scipy>=1.10.0

# Model Optimization and Deployment
onnx>=1.14.0
onnxruntime>=1.15.0
tensorrt>=8.6.0  # Optional, for NVIDIA GPUs
openvino>=2023.0.0  # Optional, for Intel optimization

# Image Quality Metrics
scikit-image>=0.21.0
pytorch-msssim>=1.0.0
lpips>=0.1.4

# Data Processing and Augmentation
albumentations>=1.3.0
imgaug>=0.4.0

# Visualization and UI
matplotlib>=3.7.0
seaborn>=0.12.0
streamlit>=1.25.0
gradio>=3.40.0

# Utilities
tqdm>=4.65.0
wandb>=0.15.0  # For experiment tracking
tensorboard>=2.13.0
pyyaml>=6.0
argparse
pathlib
json5

# Video Processing
imageio>=2.31.0
imageio-ffmpeg>=0.4.8

# Optional: For advanced model architectures
timm>=0.9.0  # PyTorch Image Models
transformers>=4.30.0  # For vision transformers if needed

# Development and Testing
pytest>=7.4.0
black>=23.0.0
flake8>=6.0.0
