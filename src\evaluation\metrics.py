"""
Evaluation Metrics for Image Sharpening

This module implements various image quality metrics including SSIM, PSNR,
LPIPS, and other evaluation measures for image sharpening performance.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from skimage.metrics import structural_similarity as ssim
from skimage.metrics import peak_signal_noise_ratio as psnr
import cv2
from typing import Union, Tuple, Optional
import lpips


class SSIMMetric(nn.Module):
    """
    Structural Similarity Index Metric (SSIM) implementation.
    
    Measures the structural similarity between two images.
    Higher values indicate better quality (range: -1 to 1, typically 0 to 1).
    """
    
    def __init__(self, window_size: int = 11, sigma: float = 1.5):
        super(SSIMMetric, self).__init__()
        self.window_size = window_size
        self.sigma = sigma
        self.channel = 1
        self.window = self._create_window(window_size, sigma)
    
    def _gaussian(self, window_size: int, sigma: float) -> torch.Tensor:
        """Create Gaussian kernel."""
        gauss = torch.Tensor([
            np.exp(-(x - window_size//2)**2/float(2*sigma**2)) 
            for x in range(window_size)
        ])
        return gauss / gauss.sum()
    
    def _create_window(self, window_size: int, sigma: float) -> torch.Tensor:
        """Create 2D Gaussian window."""
        _1D_window = self._gaussian(window_size, sigma).unsqueeze(1)
        _2D_window = _1D_window.mm(_1D_window.t()).float().unsqueeze(0).unsqueeze(0)
        return _2D_window
    
    def _ssim(
        self, 
        img1: torch.Tensor, 
        img2: torch.Tensor, 
        window: torch.Tensor,
        window_size: int,
        channel: int,
        size_average: bool = True
    ) -> torch.Tensor:
        """Compute SSIM between two images."""
        mu1 = F.conv2d(img1, window, padding=window_size//2, groups=channel)
        mu2 = F.conv2d(img2, window, padding=window_size//2, groups=channel)
        
        mu1_sq = mu1.pow(2)
        mu2_sq = mu2.pow(2)
        mu1_mu2 = mu1 * mu2
        
        sigma1_sq = F.conv2d(img1 * img1, window, padding=window_size//2, groups=channel) - mu1_sq
        sigma2_sq = F.conv2d(img2 * img2, window, padding=window_size//2, groups=channel) - mu2_sq
        sigma12 = F.conv2d(img1 * img2, window, padding=window_size//2, groups=channel) - mu1_mu2
        
        C1 = 0.01 ** 2
        C2 = 0.03 ** 2
        
        ssim_map = ((2 * mu1_mu2 + C1) * (2 * sigma12 + C2)) / ((mu1_sq + mu2_sq + C1) * (sigma1_sq + sigma2_sq + C2))
        
        if size_average:
            return ssim_map.mean()
        else:
            return ssim_map.mean(1).mean(1).mean(1)
    
    def forward(self, img1: torch.Tensor, img2: torch.Tensor) -> torch.Tensor:
        """
        Compute SSIM between two image tensors.
        
        Args:
            img1: First image tensor (B, C, H, W)
            img2: Second image tensor (B, C, H, W)
            
        Returns:
            SSIM value as tensor
        """
        (_, channel, _, _) = img1.size()
        
        if channel == self.channel and self.window.data.type() == img1.data.type():
            window = self.window
        else:
            window = self._create_window(self.window_size, self.sigma)
            
            if img1.is_cuda:
                window = window.cuda(img1.get_device())
            window = window.type_as(img1)
            
            self.window = window
            self.channel = channel
        
        return self._ssim(img1, img2, window, self.window_size, channel, True)


class PSNRMetric(nn.Module):
    """
    Peak Signal-to-Noise Ratio (PSNR) metric.
    
    Measures the ratio between the maximum possible power of a signal
    and the power of corrupting noise. Higher values indicate better quality.
    """
    
    def __init__(self, max_val: float = 1.0):
        super(PSNRMetric, self).__init__()
        self.max_val = max_val
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        Compute PSNR between predicted and target images.
        
        Args:
            pred: Predicted image tensor (B, C, H, W)
            target: Target image tensor (B, C, H, W)
            
        Returns:
            PSNR value as tensor
        """
        mse = F.mse_loss(pred, target)
        if mse == 0:
            return torch.tensor(float('inf'))
        
        psnr_value = 20 * torch.log10(self.max_val / torch.sqrt(mse))
        return psnr_value


class LPIPSMetric(nn.Module):
    """
    Learned Perceptual Image Patch Similarity (LPIPS) metric.
    
    Uses deep features to measure perceptual similarity.
    Lower values indicate better perceptual quality.
    """
    
    def __init__(self, net: str = 'alex', use_gpu: bool = True):
        super(LPIPSMetric, self).__init__()
        self.lpips_model = lpips.LPIPS(net=net, verbose=False)
        if use_gpu and torch.cuda.is_available():
            self.lpips_model = self.lpips_model.cuda()
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        Compute LPIPS between predicted and target images.
        
        Args:
            pred: Predicted image tensor (B, C, H, W) in range [0, 1]
            target: Target image tensor (B, C, H, W) in range [0, 1]
            
        Returns:
            LPIPS value as tensor
        """
        # LPIPS expects values in range [-1, 1]
        pred_norm = pred * 2.0 - 1.0
        target_norm = target * 2.0 - 1.0
        
        return self.lpips_model(pred_norm, target_norm).mean()


class MSEMetric(nn.Module):
    """Mean Squared Error metric."""
    
    def __init__(self):
        super(MSEMetric, self).__init__()
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """Compute MSE between predicted and target images."""
        return F.mse_loss(pred, target)


class MAEMetric(nn.Module):
    """Mean Absolute Error metric."""
    
    def __init__(self):
        super(MAEMetric, self).__init__()
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """Compute MAE between predicted and target images."""
        return F.l1_loss(pred, target)


class EdgePreservationMetric(nn.Module):
    """
    Edge Preservation metric for evaluating sharpening quality.
    
    Measures how well edges are preserved in the enhanced image.
    """
    
    def __init__(self):
        super(EdgePreservationMetric, self).__init__()
        # Sobel kernels for edge detection
        self.sobel_x = torch.tensor([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]], dtype=torch.float32).unsqueeze(0).unsqueeze(0)
        self.sobel_y = torch.tensor([[-1, -2, -1], [0, 0, 0], [1, 2, 1]], dtype=torch.float32).unsqueeze(0).unsqueeze(0)
    
    def _detect_edges(self, img: torch.Tensor) -> torch.Tensor:
        """Detect edges using Sobel operator."""
        if img.is_cuda:
            self.sobel_x = self.sobel_x.cuda()
            self.sobel_y = self.sobel_y.cuda()
        
        # Convert to grayscale if needed
        if img.size(1) == 3:
            gray = 0.299 * img[:, 0:1] + 0.587 * img[:, 1:2] + 0.114 * img[:, 2:3]
        else:
            gray = img
        
        # Apply Sobel filters
        edge_x = F.conv2d(gray, self.sobel_x, padding=1)
        edge_y = F.conv2d(gray, self.sobel_y, padding=1)
        
        # Compute edge magnitude
        edges = torch.sqrt(edge_x**2 + edge_y**2)
        return edges
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        Compute edge preservation score.
        
        Args:
            pred: Predicted image tensor
            target: Target image tensor
            
        Returns:
            Edge preservation score (higher is better)
        """
        pred_edges = self._detect_edges(pred)
        target_edges = self._detect_edges(target)
        
        # Compute correlation between edge maps
        pred_flat = pred_edges.view(pred_edges.size(0), -1)
        target_flat = target_edges.view(target_edges.size(0), -1)
        
        # Pearson correlation coefficient
        pred_mean = pred_flat.mean(dim=1, keepdim=True)
        target_mean = target_flat.mean(dim=1, keepdim=True)
        
        pred_centered = pred_flat - pred_mean
        target_centered = target_flat - target_mean
        
        numerator = (pred_centered * target_centered).sum(dim=1)
        denominator = torch.sqrt((pred_centered**2).sum(dim=1) * (target_centered**2).sum(dim=1))
        
        correlation = numerator / (denominator + 1e-8)
        return correlation.mean()


class MetricCalculator:
    """
    Utility class for calculating multiple metrics at once.
    """
    
    def __init__(self, device: str = 'cuda'):
        self.device = device
        self.metrics = {
            'ssim': SSIMMetric(),
            'psnr': PSNRMetric(),
            'lpips': LPIPSMetric(use_gpu=(device == 'cuda')),
            'mse': MSEMetric(),
            'mae': MAEMetric(),
            'edge_preservation': EdgePreservationMetric()
        }
        
        # Move metrics to device
        for metric in self.metrics.values():
            if hasattr(metric, 'to'):
                metric.to(device)
    
    def calculate_all_metrics(
        self, 
        pred: torch.Tensor, 
        target: torch.Tensor
    ) -> dict:
        """
        Calculate all metrics for given prediction and target.
        
        Args:
            pred: Predicted image tensor
            target: Target image tensor
            
        Returns:
            Dictionary of metric values
        """
        results = {}
        
        with torch.no_grad():
            for name, metric in self.metrics.items():
                try:
                    value = metric(pred, target)
                    results[name] = value.item() if torch.is_tensor(value) else value
                except Exception as e:
                    print(f"Error computing {name}: {e}")
                    results[name] = 0.0
        
        return results
    
    def calculate_batch_metrics(
        self, 
        pred_batch: torch.Tensor, 
        target_batch: torch.Tensor
    ) -> dict:
        """
        Calculate metrics for a batch of images.
        
        Args:
            pred_batch: Batch of predicted images
            target_batch: Batch of target images
            
        Returns:
            Dictionary of averaged metric values
        """
        batch_results = []
        
        for i in range(pred_batch.size(0)):
            pred_single = pred_batch[i:i+1]
            target_single = target_batch[i:i+1]
            
            metrics = self.calculate_all_metrics(pred_single, target_single)
            batch_results.append(metrics)
        
        # Average across batch
        averaged_results = {}
        for key in batch_results[0].keys():
            averaged_results[key] = np.mean([result[key] for result in batch_results])
        
        return averaged_results


def test_metrics():
    """Test function for metrics."""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Create test images
    pred = torch.randn(2, 3, 256, 256).to(device)
    target = torch.randn(2, 3, 256, 256).to(device)
    
    # Test individual metrics
    ssim_metric = SSIMMetric().to(device)
    psnr_metric = PSNRMetric().to(device)
    
    ssim_value = ssim_metric(pred, target)
    psnr_value = psnr_metric(pred, target)
    
    print(f"SSIM: {ssim_value.item():.4f}")
    print(f"PSNR: {psnr_value.item():.2f}")
    
    # Test metric calculator
    calculator = MetricCalculator(device)
    all_metrics = calculator.calculate_batch_metrics(pred, target)
    
    print("\nAll metrics:")
    for name, value in all_metrics.items():
        print(f"{name}: {value:.4f}")


if __name__ == "__main__":
    test_metrics()
