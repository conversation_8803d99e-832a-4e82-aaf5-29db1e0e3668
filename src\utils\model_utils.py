"""
Model Utilities

This module provides utility functions for model operations including
model conversion, optimization, and deployment helpers.
"""

import torch
import torch.nn as nn
import onnx
import onnxruntime as ort
import numpy as np
from pathlib import Path
from typing import Tuple, Optional, Dict, Any
import yaml
import time


class ModelConverter:
    """Utility class for converting models to different formats."""
    
    @staticmethod
    def pytorch_to_onnx(
        model: nn.Module,
        input_shape: Tuple[int, ...],
        output_path: str,
        opset_version: int = 11,
        dynamic_axes: Optional[Dict[str, Dict[int, str]]] = None
    ) -> bool:
        """
        Convert PyTorch model to ONNX format.
        
        Args:
            model: PyTorch model
            input_shape: Input tensor shape (C, H, W)
            output_path: Path to save ONNX model
            opset_version: ONNX opset version
            dynamic_axes: Dynamic axes specification
            
        Returns:
            True if conversion successful, False otherwise
        """
        try:
            model.eval()
            
            # Create dummy input
            dummy_input = torch.randn(1, *input_shape)
            
            # Default dynamic axes for batch dimension
            if dynamic_axes is None:
                dynamic_axes = {
                    'input': {0: 'batch_size'},
                    'output': {0: 'batch_size'}
                }
            
            # Export to ONNX
            torch.onnx.export(
                model,
                dummy_input,
                output_path,
                export_params=True,
                opset_version=opset_version,
                do_constant_folding=True,
                input_names=['input'],
                output_names=['output'],
                dynamic_axes=dynamic_axes
            )
            
            print(f"Model successfully converted to ONNX: {output_path}")
            return True
            
        except Exception as e:
            print(f"ONNX conversion failed: {e}")
            return False
    
    @staticmethod
    def verify_onnx_model(onnx_path: str, pytorch_model: nn.Module, input_shape: Tuple[int, ...]) -> bool:
        """
        Verify ONNX model against PyTorch model.
        
        Args:
            onnx_path: Path to ONNX model
            pytorch_model: Original PyTorch model
            input_shape: Input tensor shape
            
        Returns:
            True if models produce similar outputs, False otherwise
        """
        try:
            # Load ONNX model
            ort_session = ort.InferenceSession(onnx_path)
            
            # Create test input
            test_input = torch.randn(1, *input_shape)
            
            # PyTorch inference
            pytorch_model.eval()
            with torch.no_grad():
                pytorch_output = pytorch_model(test_input).numpy()
            
            # ONNX inference
            ort_inputs = {ort_session.get_inputs()[0].name: test_input.numpy()}
            onnx_output = ort_session.run(None, ort_inputs)[0]
            
            # Compare outputs
            max_diff = np.max(np.abs(pytorch_output - onnx_output))
            print(f"Maximum difference between PyTorch and ONNX outputs: {max_diff}")
            
            # Consider successful if difference is small
            return max_diff < 1e-5
            
        except Exception as e:
            print(f"ONNX verification failed: {e}")
            return False


class ModelOptimizer:
    """Utility class for model optimization."""
    
    @staticmethod
    def quantize_model(model: nn.Module, calibration_data: torch.Tensor) -> nn.Module:
        """
        Apply dynamic quantization to model.
        
        Args:
            model: PyTorch model to quantize
            calibration_data: Data for calibration
            
        Returns:
            Quantized model
        """
        try:
            # Prepare model for quantization
            model.eval()
            
            # Apply dynamic quantization
            quantized_model = torch.quantization.quantize_dynamic(
                model,
                {nn.Linear, nn.Conv2d},
                dtype=torch.qint8
            )
            
            print("Model successfully quantized")
            return quantized_model
            
        except Exception as e:
            print(f"Quantization failed: {e}")
            return model
    
    @staticmethod
    def optimize_for_inference(model: nn.Module) -> nn.Module:
        """
        Optimize model for inference.
        
        Args:
            model: PyTorch model
            
        Returns:
            Optimized model
        """
        try:
            model.eval()
            
            # Try TorchScript optimization
            try:
                scripted_model = torch.jit.script(model)
                scripted_model = torch.jit.optimize_for_inference(scripted_model)
                print("Model optimized with TorchScript")
                return scripted_model
            except Exception as e:
                print(f"TorchScript optimization failed: {e}")
                return model
                
        except Exception as e:
            print(f"Inference optimization failed: {e}")
            return model


class ModelProfiler:
    """Utility class for profiling model performance."""
    
    @staticmethod
    def profile_model(
        model: nn.Module,
        input_shape: Tuple[int, ...],
        num_runs: int = 100,
        warmup_runs: int = 10
    ) -> Dict[str, float]:
        """
        Profile model performance.
        
        Args:
            model: PyTorch model
            input_shape: Input tensor shape
            num_runs: Number of timing runs
            warmup_runs: Number of warmup runs
            
        Returns:
            Dictionary with profiling results
        """
        model.eval()
        device = next(model.parameters()).device
        
        # Create test input
        test_input = torch.randn(1, *input_shape).to(device)
        
        # Warmup
        with torch.no_grad():
            for _ in range(warmup_runs):
                _ = model(test_input)
        
        # Synchronize if using CUDA
        if device.type == 'cuda':
            torch.cuda.synchronize()
        
        # Timing runs
        times = []
        with torch.no_grad():
            for _ in range(num_runs):
                start_time = time.time()
                output = model(test_input)
                
                if device.type == 'cuda':
                    torch.cuda.synchronize()
                
                end_time = time.time()
                times.append(end_time - start_time)
        
        times = np.array(times)
        
        # Calculate statistics
        results = {
            'mean_time_ms': np.mean(times) * 1000,
            'std_time_ms': np.std(times) * 1000,
            'min_time_ms': np.min(times) * 1000,
            'max_time_ms': np.max(times) * 1000,
            'fps': 1.0 / np.mean(times),
            'throughput': 1.0 / np.mean(times),  # Images per second
        }
        
        return results
    
    @staticmethod
    def get_model_size(model: nn.Module) -> Dict[str, Any]:
        """
        Get model size information.
        
        Args:
            model: PyTorch model
            
        Returns:
            Dictionary with size information
        """
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        # Estimate memory usage (assuming float32)
        param_size_mb = total_params * 4 / (1024 * 1024)
        
        return {
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'parameter_size_mb': param_size_mb,
            'model_size_mb': param_size_mb,  # Simplified estimate
        }


class DeploymentHelper:
    """Helper class for model deployment."""
    
    @staticmethod
    def create_deployment_package(
        model_path: str,
        config_path: str,
        output_dir: str,
        include_onnx: bool = True,
        include_quantized: bool = True
    ):
        """
        Create a deployment package with model and configurations.
        
        Args:
            model_path: Path to PyTorch model
            config_path: Path to configuration file
            output_dir: Output directory for deployment package
            include_onnx: Whether to include ONNX version
            include_quantized: Whether to include quantized version
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        print(f"Creating deployment package in {output_dir}")
        
        # Load model and config
        model = torch.load(model_path, map_location='cpu')
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # Copy original files
        import shutil
        shutil.copy2(model_path, output_path / "model.pth")
        shutil.copy2(config_path, output_path / "config.yaml")
        
        # Create ONNX version if requested
        if include_onnx:
            try:
                # Determine input shape from config
                input_shape = (3, 256, 256)  # Default shape
                if 'data' in config and 'crop_size' in config['data']:
                    h, w = config['data']['crop_size']
                    input_shape = (3, h, w)
                
                onnx_path = output_path / "model.onnx"
                ModelConverter.pytorch_to_onnx(model, input_shape, str(onnx_path))
                
            except Exception as e:
                print(f"Failed to create ONNX version: {e}")
        
        # Create quantized version if requested
        if include_quantized:
            try:
                # Create dummy calibration data
                calibration_data = torch.randn(10, 3, 256, 256)
                quantized_model = ModelOptimizer.quantize_model(model, calibration_data)
                
                quantized_path = output_path / "model_quantized.pth"
                torch.save(quantized_model.state_dict(), quantized_path)
                
            except Exception as e:
                print(f"Failed to create quantized version: {e}")
        
        # Create deployment info
        deployment_info = {
            'model_info': ModelProfiler.get_model_size(model),
            'config': config,
            'files': {
                'pytorch_model': 'model.pth',
                'config': 'config.yaml',
                'onnx_model': 'model.onnx' if include_onnx else None,
                'quantized_model': 'model_quantized.pth' if include_quantized else None,
            },
            'usage': {
                'pytorch': 'torch.load("model.pth")',
                'onnx': 'onnxruntime.InferenceSession("model.onnx")',
                'config': 'yaml.safe_load(open("config.yaml"))',
            }
        }
        
        with open(output_path / "deployment_info.yaml", 'w') as f:
            yaml.dump(deployment_info, f, default_flow_style=False)
        
        print("Deployment package created successfully!")
        print(f"Files created:")
        for file in output_path.iterdir():
            print(f"  - {file.name}")


def main():
    """Test utility functions."""
    print("Model Utilities Test")
    print("=" * 30)
    
    # Create a simple test model
    class TestModel(nn.Module):
        def __init__(self):
            super().__init__()
            self.conv = nn.Conv2d(3, 16, 3, padding=1)
            self.relu = nn.ReLU()
            self.conv2 = nn.Conv2d(16, 3, 3, padding=1)
        
        def forward(self, x):
            x = self.relu(self.conv(x))
            x = self.conv2(x)
            return x
    
    model = TestModel()
    
    # Test profiling
    print("Profiling model...")
    profile_results = ModelProfiler.profile_model(model, (3, 256, 256))
    for key, value in profile_results.items():
        print(f"  {key}: {value:.4f}")
    
    # Test size calculation
    print("\nModel size info...")
    size_info = ModelProfiler.get_model_size(model)
    for key, value in size_info.items():
        print(f"  {key}: {value}")


if __name__ == "__main__":
    main()
