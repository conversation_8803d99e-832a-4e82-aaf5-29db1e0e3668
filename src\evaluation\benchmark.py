"""
Benchmarking System for Image Sharpening

This module implements comprehensive benchmarking including SSIM calculation,
performance profiling, and evaluation on diverse image categories.
"""

import torch
import cv2
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import time
import json
import argparse
from typing import Dict, List, Tuple, Optional
from tqdm import tqdm
import yaml

from ..models.student_models import StudentModelFactory
from ..inference.image_processor import ImageProcessor
from .metrics import MetricCalculator


class BenchmarkSuite:
    """
    Comprehensive benchmarking suite for image sharpening models.
    
    Evaluates models on quality metrics, performance, and diverse image categories.
    """
    
    def __init__(
        self,
        model_path: str,
        config_path: str = "configs/config.yaml",
        device: Optional[str] = None
    ):
        """
        Initialize the benchmark suite.
        
        Args:
            model_path: Path to trained model
            config_path: Path to configuration file
            device: Device to use for evaluation
        """
        self.model_path = model_path
        self.config_path = config_path
        
        # Load configuration
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        # Set device
        if device is None:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
        
        print(f"Benchmarking on device: {self.device}")
        
        # Initialize processor and metrics
        self.processor = ImageProcessor(model_path, config_path, str(self.device))
        self.metrics_calculator = MetricCalculator(str(self.device))
        
        # Results storage
        self.results = {
            'quality_metrics': [],
            'performance_metrics': [],
            'category_results': {},
            'model_info': {}
        }
    
    def benchmark_quality(
        self,
        test_dir: str,
        ground_truth_dir: str,
        categories: Optional[List[str]] = None
    ) -> Dict:
        """
        Benchmark image quality metrics.
        
        Args:
            test_dir: Directory with test images
            ground_truth_dir: Directory with ground truth images
            categories: List of image categories to evaluate
            
        Returns:
            Dictionary with quality benchmark results
        """
        print("Starting quality benchmarking...")
        
        if categories is None:
            categories = ['people', 'nature', 'text', 'animals', 'games']
        
        test_path = Path(test_dir)
        gt_path = Path(ground_truth_dir)
        
        all_results = []
        category_results = {cat: [] for cat in categories}
        
        # Find test images
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        test_images = []
        for ext in image_extensions:
            test_images.extend(test_path.glob(f"*{ext}"))
            test_images.extend(test_path.glob(f"*{ext.upper()}"))
        
        print(f"Found {len(test_images)} test images")
        
        for image_file in tqdm(test_images, desc="Quality evaluation"):
            try:
                # Find corresponding ground truth
                gt_file = gt_path / image_file.name
                if not gt_file.exists():
                    # Try different naming conventions
                    gt_file = gt_path / image_file.name.replace('_degraded', '').replace('_low', '')
                
                if not gt_file.exists():
                    print(f"Warning: No ground truth found for {image_file.name}")
                    continue
                
                # Process image and calculate metrics
                enhanced_image, metrics = self.processor.process_image(
                    str(image_file),
                    return_metrics=True,
                    ground_truth_path=str(gt_file)
                )
                
                # Determine category
                category = 'unknown'
                for cat in categories:
                    if cat.lower() in image_file.name.lower():
                        category = cat
                        break
                
                # Store results
                result = {
                    'image_name': image_file.name,
                    'category': category,
                    **metrics
                }
                
                all_results.append(result)
                category_results[category].append(result)
                
            except Exception as e:
                print(f"Error processing {image_file.name}: {e}")
        
        # Calculate summary statistics
        df = pd.DataFrame(all_results)
        
        summary = {
            'total_images': len(all_results),
            'overall_metrics': {
                'ssim_mean': df['ssim'].mean(),
                'ssim_std': df['ssim'].std(),
                'psnr_mean': df['psnr'].mean(),
                'psnr_std': df['psnr'].std(),
                'lpips_mean': df['lpips'].mean(),
                'lpips_std': df['lpips'].std(),
            },
            'category_metrics': {},
            'target_achievement': {
                'ssim_above_90': (df['ssim'] > 0.9).sum() / len(df) * 100,
                'ssim_above_85': (df['ssim'] > 0.85).sum() / len(df) * 100,
            }
        }
        
        # Category-wise analysis
        for category in categories:
            cat_df = df[df['category'] == category]
            if len(cat_df) > 0:
                summary['category_metrics'][category] = {
                    'count': len(cat_df),
                    'ssim_mean': cat_df['ssim'].mean(),
                    'psnr_mean': cat_df['psnr'].mean(),
                    'lpips_mean': cat_df['lpips'].mean(),
                }
        
        self.results['quality_metrics'] = all_results
        self.results['quality_summary'] = summary
        
        return summary
    
    def benchmark_performance(
        self,
        test_images: List[str],
        batch_sizes: List[int] = [1, 4, 8, 16],
        num_warmup: int = 10,
        num_iterations: int = 100
    ) -> Dict:
        """
        Benchmark inference performance.
        
        Args:
            test_images: List of test image paths
            batch_sizes: List of batch sizes to test
            num_warmup: Number of warmup iterations
            num_iterations: Number of timing iterations
            
        Returns:
            Dictionary with performance benchmark results
        """
        print("Starting performance benchmarking...")
        
        performance_results = []
        
        # Load test images
        test_tensors = []
        for img_path in test_images[:min(len(test_images), 16)]:  # Limit for memory
            image = cv2.imread(img_path)
            if image is not None:
                tensor = self.processor._preprocess_image(image)
                test_tensors.append(tensor)
        
        if not test_tensors:
            print("No valid test images found")
            return {}
        
        print(f"Using {len(test_tensors)} test images for performance evaluation")
        
        for batch_size in batch_sizes:
            print(f"Testing batch size: {batch_size}")
            
            # Create batches
            if batch_size == 1:
                test_batch = test_tensors[0]
            else:
                # Repeat first image to create batch
                test_batch = test_tensors[0].repeat(batch_size, 1, 1, 1)
            
            # Warmup
            with torch.no_grad():
                for _ in range(num_warmup):
                    _ = self.processor.model(test_batch)
            
            # Synchronize GPU
            if self.device.type == 'cuda':
                torch.cuda.synchronize()
            
            # Timing
            times = []
            with torch.no_grad():
                for _ in range(num_iterations):
                    start_time = time.time()
                    output = self.processor.model(test_batch)
                    
                    if self.device.type == 'cuda':
                        torch.cuda.synchronize()
                    
                    end_time = time.time()
                    times.append(end_time - start_time)
            
            # Calculate statistics
            times = np.array(times)
            avg_time = np.mean(times)
            std_time = np.std(times)
            min_time = np.min(times)
            max_time = np.max(times)
            
            # Calculate FPS
            fps = batch_size / avg_time
            
            result = {
                'batch_size': batch_size,
                'avg_time_ms': avg_time * 1000,
                'std_time_ms': std_time * 1000,
                'min_time_ms': min_time * 1000,
                'max_time_ms': max_time * 1000,
                'fps': fps,
                'throughput': fps,  # Images per second
            }
            
            performance_results.append(result)
            
            print(f"  Batch {batch_size}: {avg_time*1000:.2f}±{std_time*1000:.2f}ms, {fps:.1f} FPS")
        
        # Model information
        model_info = {
            'total_parameters': sum(p.numel() for p in self.processor.model.parameters()),
            'trainable_parameters': sum(p.numel() for p in self.processor.model.parameters() if p.requires_grad),
            'model_size_mb': sum(p.numel() for p in self.processor.model.parameters()) * 4 / (1024 * 1024),
            'device': str(self.device),
            'model_type': self.config['model']['student']['name']
        }
        
        self.results['performance_metrics'] = performance_results
        self.results['model_info'] = model_info
        
        return {
            'performance_results': performance_results,
            'model_info': model_info
        }
    
    def benchmark_resolution_scaling(
        self,
        test_image: str,
        resolutions: List[Tuple[int, int]] = [(256, 256), (512, 512), (1024, 1024), (1920, 1080)]
    ) -> Dict:
        """
        Benchmark performance across different resolutions.
        
        Args:
            test_image: Path to test image
            resolutions: List of (width, height) tuples to test
            
        Returns:
            Dictionary with resolution scaling results
        """
        print("Benchmarking resolution scaling...")
        
        # Load base image
        base_image = cv2.imread(test_image)
        if base_image is None:
            print(f"Could not load test image: {test_image}")
            return {}
        
        scaling_results = []
        
        for width, height in resolutions:
            print(f"Testing resolution: {width}x{height}")
            
            # Resize image
            resized_image = cv2.resize(base_image, (width, height))
            
            # Preprocess
            input_tensor = self.processor._preprocess_image(resized_image)
            
            # Warmup
            with torch.no_grad():
                for _ in range(5):
                    _ = self.processor.model(input_tensor)
            
            # Timing
            times = []
            with torch.no_grad():
                for _ in range(20):
                    if self.device.type == 'cuda':
                        torch.cuda.synchronize()
                    
                    start_time = time.time()
                    output = self.processor.model(input_tensor)
                    
                    if self.device.type == 'cuda':
                        torch.cuda.synchronize()
                    
                    end_time = time.time()
                    times.append(end_time - start_time)
            
            avg_time = np.mean(times)
            fps = 1.0 / avg_time
            
            result = {
                'resolution': f"{width}x{height}",
                'width': width,
                'height': height,
                'pixels': width * height,
                'avg_time_ms': avg_time * 1000,
                'fps': fps,
                'megapixels_per_second': (width * height * fps) / 1e6
            }
            
            scaling_results.append(result)
            print(f"  {width}x{height}: {avg_time*1000:.2f}ms, {fps:.1f} FPS")
        
        return {'resolution_scaling': scaling_results}
    
    def generate_report(self, output_dir: str = "results/benchmark"):
        """
        Generate comprehensive benchmark report.
        
        Args:
            output_dir: Directory to save report files
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        print(f"Generating benchmark report in {output_dir}")
        
        # Save raw results
        with open(output_path / "benchmark_results.json", 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        # Generate summary report
        self._generate_summary_report(output_path)
        
        # Generate visualizations
        self._generate_visualizations(output_path)
        
        print("Benchmark report generated successfully!")
    
    def _generate_summary_report(self, output_path: Path):
        """Generate text summary report."""
        with open(output_path / "benchmark_summary.txt", 'w') as f:
            f.write("IMAGE SHARPENING MODEL BENCHMARK REPORT\n")
            f.write("=" * 50 + "\n\n")
            
            # Model information
            if 'model_info' in self.results:
                info = self.results['model_info']
                f.write("MODEL INFORMATION:\n")
                f.write(f"  Model Type: {info.get('model_type', 'Unknown')}\n")
                f.write(f"  Total Parameters: {info.get('total_parameters', 0):,}\n")
                f.write(f"  Model Size: {info.get('model_size_mb', 0):.2f} MB\n")
                f.write(f"  Device: {info.get('device', 'Unknown')}\n\n")
            
            # Quality metrics
            if 'quality_summary' in self.results:
                summary = self.results['quality_summary']
                f.write("QUALITY METRICS:\n")
                f.write(f"  Total Images Evaluated: {summary['total_images']}\n")
                
                overall = summary['overall_metrics']
                f.write(f"  Average SSIM: {overall['ssim_mean']:.4f} ± {overall['ssim_std']:.4f}\n")
                f.write(f"  Average PSNR: {overall['psnr_mean']:.2f} ± {overall['psnr_std']:.2f}\n")
                f.write(f"  Average LPIPS: {overall['lpips_mean']:.4f} ± {overall['lpips_std']:.4f}\n")
                
                targets = summary['target_achievement']
                f.write(f"  Images with SSIM > 0.90: {targets['ssim_above_90']:.1f}%\n")
                f.write(f"  Images with SSIM > 0.85: {targets['ssim_above_85']:.1f}%\n\n")
                
                # Category breakdown
                f.write("CATEGORY BREAKDOWN:\n")
                for category, metrics in summary['category_metrics'].items():
                    f.write(f"  {category.upper()}:\n")
                    f.write(f"    Count: {metrics['count']}\n")
                    f.write(f"    SSIM: {metrics['ssim_mean']:.4f}\n")
                    f.write(f"    PSNR: {metrics['psnr_mean']:.2f}\n\n")
            
            # Performance metrics
            if 'performance_metrics' in self.results:
                f.write("PERFORMANCE METRICS:\n")
                for result in self.results['performance_metrics']:
                    f.write(f"  Batch Size {result['batch_size']}:\n")
                    f.write(f"    Average Time: {result['avg_time_ms']:.2f}ms\n")
                    f.write(f"    FPS: {result['fps']:.1f}\n")
                    f.write(f"    Throughput: {result['throughput']:.1f} images/sec\n\n")
    
    def _generate_visualizations(self, output_path: Path):
        """Generate visualization plots."""
        plt.style.use('seaborn-v0_8')
        
        # Quality metrics visualization
        if 'quality_metrics' in self.results and self.results['quality_metrics']:
            df = pd.DataFrame(self.results['quality_metrics'])
            
            # SSIM distribution
            plt.figure(figsize=(12, 8))
            
            plt.subplot(2, 2, 1)
            plt.hist(df['ssim'], bins=30, alpha=0.7, color='blue')
            plt.axvline(0.9, color='red', linestyle='--', label='Target (0.9)')
            plt.xlabel('SSIM')
            plt.ylabel('Frequency')
            plt.title('SSIM Distribution')
            plt.legend()
            
            # Category comparison
            plt.subplot(2, 2, 2)
            category_ssim = df.groupby('category')['ssim'].mean().sort_values(ascending=False)
            category_ssim.plot(kind='bar', color='green', alpha=0.7)
            plt.xlabel('Category')
            plt.ylabel('Average SSIM')
            plt.title('SSIM by Category')
            plt.xticks(rotation=45)
            
            # PSNR vs SSIM scatter
            plt.subplot(2, 2, 3)
            plt.scatter(df['ssim'], df['psnr'], alpha=0.6)
            plt.xlabel('SSIM')
            plt.ylabel('PSNR (dB)')
            plt.title('PSNR vs SSIM')
            
            # Performance metrics
            if 'performance_metrics' in self.results:
                plt.subplot(2, 2, 4)
                perf_df = pd.DataFrame(self.results['performance_metrics'])
                plt.plot(perf_df['batch_size'], perf_df['fps'], 'o-', color='orange')
                plt.xlabel('Batch Size')
                plt.ylabel('FPS')
                plt.title('Performance vs Batch Size')
                plt.grid(True, alpha=0.3)
            
            plt.tight_layout()
            plt.savefig(output_path / "benchmark_visualizations.png", dpi=300, bbox_inches='tight')
            plt.close()


def main():
    """Main benchmarking function."""
    parser = argparse.ArgumentParser(description="Benchmark Image Sharpening Model")
    parser.add_argument("--model", type=str, required=True, help="Path to trained model")
    parser.add_argument("--config", type=str, default="configs/config.yaml", help="Config file")
    parser.add_argument("--test_dir", type=str, required=True, help="Test images directory")
    parser.add_argument("--ground_truth_dir", type=str, help="Ground truth images directory")
    parser.add_argument("--output_dir", type=str, default="results/benchmark", help="Output directory")
    parser.add_argument("--quality_only", action="store_true", help="Run quality benchmark only")
    parser.add_argument("--performance_only", action="store_true", help="Run performance benchmark only")
    
    args = parser.parse_args()
    
    # Create benchmark suite
    benchmark = BenchmarkSuite(args.model, args.config)
    
    # Run quality benchmark
    if not args.performance_only and args.ground_truth_dir:
        benchmark.benchmark_quality(args.test_dir, args.ground_truth_dir)
    
    # Run performance benchmark
    if not args.quality_only:
        # Find test images
        test_images = list(Path(args.test_dir).glob("*.png"))[:10]  # Limit for performance test
        test_images = [str(img) for img in test_images]
        
        if test_images:
            benchmark.benchmark_performance(test_images)
            
            # Resolution scaling test
            benchmark.benchmark_resolution_scaling(test_images[0])
    
    # Generate report
    benchmark.generate_report(args.output_dir)


if __name__ == "__main__":
    main()
