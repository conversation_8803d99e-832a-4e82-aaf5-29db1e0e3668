"""
Knowledge Distillation Training Pipeline

This module implements the teacher-student training framework with appropriate
loss functions and distillation techniques for image sharpening.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter
import numpy as np
import yaml
import os
from tqdm import tqdm
from typing import Dict, Tuple, Optional, List
import time
import wandb

from ..models.teacher_models import TeacherModelFactory
from ..models.student_models import StudentModelFactory
from ..data.dataloader import create_dataloaders
from ..evaluation.metrics import SSIMMetric, PSNRMetric, LPIPSMetric


class DistillationLoss(nn.Module):
    """
    Combined loss function for knowledge distillation.
    
    Combines multiple loss components:
    - MSE loss with ground truth
    - Perceptual loss
    - Knowledge distillation loss
    - SSIM loss
    """
    
    def __init__(
        self,
        mse_weight: float = 1.0,
        perceptual_weight: float = 0.1,
        distillation_weight: float = 0.5,
        ssim_weight: float = 0.1,
        temperature: float = 4.0
    ):
        super(DistillationLoss, self).__init__()
        self.mse_weight = mse_weight
        self.perceptual_weight = perceptual_weight
        self.distillation_weight = distillation_weight
        self.ssim_weight = ssim_weight
        self.temperature = temperature
        
        # Loss functions
        self.mse_loss = nn.MSELoss()
        self.ssim_metric = SSIMMetric()
        
        # Perceptual loss using VGG features
        self.vgg = self._create_vgg_feature_extractor()
        
    def _create_vgg_feature_extractor(self):
        """Create VGG feature extractor for perceptual loss."""
        import torchvision.models as models
        vgg = models.vgg19(pretrained=True).features[:16]  # Up to relu3_3
        for param in vgg.parameters():
            param.requires_grad = False
        return vgg
    
    def perceptual_loss(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """Compute perceptual loss using VGG features."""
        pred_features = self.vgg(pred)
        target_features = self.vgg(target)
        return self.mse_loss(pred_features, target_features)
    
    def distillation_loss(
        self, 
        student_output: torch.Tensor, 
        teacher_output: torch.Tensor
    ) -> torch.Tensor:
        """Compute knowledge distillation loss."""
        # Soft targets from teacher
        teacher_soft = F.softmax(teacher_output / self.temperature, dim=1)
        student_soft = F.log_softmax(student_output / self.temperature, dim=1)
        
        # KL divergence loss
        kl_loss = F.kl_div(student_soft, teacher_soft, reduction='batchmean')
        return kl_loss * (self.temperature ** 2)
    
    def ssim_loss(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """Compute SSIM-based loss."""
        ssim_value = self.ssim_metric(pred, target)
        return 1.0 - ssim_value
    
    def forward(
        self,
        student_output: torch.Tensor,
        teacher_output: torch.Tensor,
        ground_truth: torch.Tensor
    ) -> Tuple[torch.Tensor, Dict[str, float]]:
        """
        Compute combined distillation loss.
        
        Args:
            student_output: Output from student model
            teacher_output: Output from teacher model
            ground_truth: Ground truth images
            
        Returns:
            Tuple of (total_loss, loss_components)
        """
        losses = {}
        
        # MSE loss with ground truth
        mse_loss = self.mse_loss(student_output, ground_truth)
        losses['mse'] = mse_loss.item()
        
        # Perceptual loss
        perceptual_loss = self.perceptual_loss(student_output, ground_truth)
        losses['perceptual'] = perceptual_loss.item()
        
        # Knowledge distillation loss
        kd_loss = self.mse_loss(student_output, teacher_output.detach())
        losses['distillation'] = kd_loss.item()
        
        # SSIM loss
        ssim_loss = self.ssim_loss(student_output, ground_truth)
        losses['ssim'] = ssim_loss.item()
        
        # Combine losses
        total_loss = (
            self.mse_weight * mse_loss +
            self.perceptual_weight * perceptual_loss +
            self.distillation_weight * kd_loss +
            self.ssim_weight * ssim_loss
        )
        
        losses['total'] = total_loss.item()
        
        return total_loss, losses


class DistillationTrainer:
    """
    Knowledge distillation trainer for image sharpening.
    """
    
    def __init__(self, config_path: str = "configs/config.yaml"):
        """Initialize the distillation trainer."""
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Initialize models
        self.teacher = self._create_teacher_model()
        self.student = self._create_student_model()
        
        # Initialize loss function
        self.criterion = DistillationLoss(
            mse_weight=self.config['training']['loss']['mse_weight'],
            perceptual_weight=self.config['training']['loss']['perceptual_weight'],
            distillation_weight=self.config['training']['loss']['distillation_weight'],
            ssim_weight=self.config['training']['loss']['ssim_weight'],
            temperature=self.config['training']['distillation']['temperature']
        )
        
        # Initialize optimizer
        self.optimizer = optim.Adam(
            self.student.parameters(),
            lr=self.config['training']['learning_rate'],
            weight_decay=self.config['training']['weight_decay']
        )
        
        # Initialize scheduler
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer,
            T_max=self.config['training']['epochs']
        )
        
        # Initialize data loaders
        self.train_loader, self.val_loader = create_dataloaders(config_path)
        
        # Initialize logging
        self.writer = SummaryWriter(log_dir='logs/tensorboard')
        if self.config['logging']['use_wandb']:
            wandb.init(project="image_sharpening_kd", config=self.config)
        
        # Training state
        self.current_epoch = 0
        self.best_ssim = 0.0
        
    def _create_teacher_model(self) -> nn.Module:
        """Create and load teacher model."""
        teacher = TeacherModelFactory.create_teacher(
            self.config['model']['teacher']['name'],
            scale_factor=self.config['model']['teacher']['scale_factor']
        )
        
        # Load pretrained weights if available
        teacher_path = os.path.join('models/teacher', 'teacher_model.pth')
        if os.path.exists(teacher_path):
            teacher.load_state_dict(torch.load(teacher_path, map_location=self.device))
            print(f"Loaded teacher model from {teacher_path}")
        
        teacher.to(self.device)
        teacher.eval()  # Teacher is always in eval mode
        
        # Freeze teacher parameters
        for param in teacher.parameters():
            param.requires_grad = False
        
        return teacher
    
    def _create_student_model(self) -> nn.Module:
        """Create student model."""
        student = StudentModelFactory.create_student(
            self.config['model']['student']['name'],
            scale_factor=self.config['model']['student']['scale_factor'],
            base_channels=self.config['model']['student']['channels'],
            num_blocks=self.config['model']['student']['num_blocks'],
            use_attention=self.config['model']['student']['use_attention']
        )
        
        student.to(self.device)
        return student
    
    def train_epoch(self) -> Dict[str, float]:
        """Train for one epoch."""
        self.student.train()
        
        epoch_losses = {
            'total': 0.0,
            'mse': 0.0,
            'perceptual': 0.0,
            'distillation': 0.0,
            'ssim': 0.0
        }
        
        num_batches = len(self.train_loader)
        
        with tqdm(self.train_loader, desc=f"Epoch {self.current_epoch}") as pbar:
            for batch_idx, (degraded, ground_truth) in enumerate(pbar):
                degraded = degraded.to(self.device)
                ground_truth = ground_truth.to(self.device)
                
                # Forward pass
                self.optimizer.zero_grad()
                
                # Get teacher output (no gradients)
                with torch.no_grad():
                    teacher_output = self.teacher(degraded)
                
                # Get student output
                student_output = self.student(degraded)
                
                # Compute loss
                loss, loss_components = self.criterion(
                    student_output, teacher_output, ground_truth
                )
                
                # Backward pass
                loss.backward()
                self.optimizer.step()
                
                # Update epoch losses
                for key, value in loss_components.items():
                    epoch_losses[key] += value
                
                # Update progress bar
                pbar.set_postfix({
                    'Loss': f"{loss.item():.4f}",
                    'LR': f"{self.optimizer.param_groups[0]['lr']:.6f}"
                })
                
                # Log to tensorboard
                if batch_idx % self.config['logging']['log_interval'] == 0:
                    global_step = self.current_epoch * num_batches + batch_idx
                    for key, value in loss_components.items():
                        self.writer.add_scalar(f'Train/{key}', value, global_step)
        
        # Average losses
        for key in epoch_losses:
            epoch_losses[key] /= num_batches
        
        return epoch_losses
    
    def validate(self) -> Dict[str, float]:
        """Validate the model."""
        self.student.eval()
        
        val_losses = {
            'total': 0.0,
            'mse': 0.0,
            'perceptual': 0.0,
            'distillation': 0.0,
            'ssim': 0.0
        }
        
        ssim_metric = SSIMMetric()
        psnr_metric = PSNRMetric()
        
        num_batches = len(self.val_loader)
        total_ssim = 0.0
        total_psnr = 0.0
        
        with torch.no_grad():
            for degraded, ground_truth in tqdm(self.val_loader, desc="Validation"):
                degraded = degraded.to(self.device)
                ground_truth = ground_truth.to(self.device)
                
                # Forward pass
                teacher_output = self.teacher(degraded)
                student_output = self.student(degraded)
                
                # Compute loss
                loss, loss_components = self.criterion(
                    student_output, teacher_output, ground_truth
                )
                
                # Update losses
                for key, value in loss_components.items():
                    val_losses[key] += value
                
                # Compute metrics
                ssim_value = ssim_metric(student_output, ground_truth)
                psnr_value = psnr_metric(student_output, ground_truth)
                
                total_ssim += ssim_value
                total_psnr += psnr_value
        
        # Average losses and metrics
        for key in val_losses:
            val_losses[key] /= num_batches
        
        avg_ssim = total_ssim / num_batches
        avg_psnr = total_psnr / num_batches
        
        val_losses['ssim_metric'] = avg_ssim
        val_losses['psnr_metric'] = avg_psnr
        
        return val_losses
    
    def save_checkpoint(self, is_best: bool = False):
        """Save model checkpoint."""
        checkpoint = {
            'epoch': self.current_epoch,
            'student_state_dict': self.student.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_ssim': self.best_ssim,
            'config': self.config
        }
        
        # Save regular checkpoint
        checkpoint_path = os.path.join('models/student', f'checkpoint_epoch_{self.current_epoch}.pth')
        os.makedirs(os.path.dirname(checkpoint_path), exist_ok=True)
        torch.save(checkpoint, checkpoint_path)
        
        # Save best model
        if is_best:
            best_path = os.path.join('models/student', 'best_model.pth')
            torch.save(checkpoint, best_path)
            print(f"New best model saved with SSIM: {self.best_ssim:.4f}")
    
    def train(self):
        """Main training loop."""
        print(f"Starting training for {self.config['training']['epochs']} epochs")
        print(f"Device: {self.device}")
        print(f"Teacher: {self.config['model']['teacher']['name']}")
        print(f"Student: {self.config['model']['student']['name']}")
        
        for epoch in range(self.config['training']['epochs']):
            self.current_epoch = epoch
            
            # Train
            train_losses = self.train_epoch()
            
            # Validate
            val_losses = self.validate()
            
            # Update scheduler
            self.scheduler.step()
            
            # Check if best model
            current_ssim = val_losses['ssim_metric']
            is_best = current_ssim > self.best_ssim
            if is_best:
                self.best_ssim = current_ssim
            
            # Save checkpoint
            if epoch % self.config['logging']['save_interval'] == 0 or is_best:
                self.save_checkpoint(is_best)
            
            # Log to tensorboard and wandb
            for key, value in train_losses.items():
                self.writer.add_scalar(f'Train/{key}', value, epoch)
            
            for key, value in val_losses.items():
                self.writer.add_scalar(f'Val/{key}', value, epoch)
            
            if self.config['logging']['use_wandb']:
                wandb.log({
                    **{f'train_{k}': v for k, v in train_losses.items()},
                    **{f'val_{k}': v for k, v in val_losses.items()},
                    'epoch': epoch,
                    'learning_rate': self.optimizer.param_groups[0]['lr']
                })
            
            # Print epoch summary
            print(f"Epoch {epoch}: Train Loss: {train_losses['total']:.4f}, "
                  f"Val SSIM: {val_losses['ssim_metric']:.4f}, "
                  f"Val PSNR: {val_losses['psnr_metric']:.2f}")
        
        print(f"Training completed! Best SSIM: {self.best_ssim:.4f}")


def main():
    """Main training function."""
    trainer = DistillationTrainer()
    trainer.train()


if __name__ == "__main__":
    main()
