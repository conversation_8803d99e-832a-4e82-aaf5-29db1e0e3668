"""
Dataset Generator for Image Sharpening with Knowledge Distillation

This module creates training datasets by simulating video conferencing conditions
through downscaling and upscaling operations using bicubic/bilinear interpolation.
"""

import os
import cv2
import numpy as np
from PIL import Image
import albumentations as A
from pathlib import Path
from typing import Tuple, List, Optional
import yaml
from tqdm import tqdm
import argparse


class DatasetGenerator:
    """
    Generates training datasets for image sharpening by simulating
    video conferencing degradation conditions.
    """
    
    def __init__(self, config_path: str = "configs/config.yaml"):
        """Initialize the dataset generator with configuration."""
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        self.input_size = tuple(self.config['data']['input_resolution'])
        self.target_size = tuple(self.config['data']['target_resolution'])
        self.crop_size = tuple(self.config['data']['crop_size'])
        
        # Define augmentation pipeline
        self.augmentation = <PERSON>.Compose([
            A.HorizontalFlip(p=self.config['data']['augmentation']['horizontal_flip']),
            A.VerticalFlip(p=self.config['data']['augmentation']['vertical_flip']),
            A.Rotate(limit=self.config['data']['augmentation']['rotation'], p=0.5),
            A.ColorJitter(
                brightness=self.config['data']['augmentation']['brightness'],
                contrast=self.config['data']['augmentation']['contrast'],
                saturation=self.config['data']['augmentation']['saturation'],
                hue=self.config['data']['augmentation']['hue'],
                p=0.5
            ),
        ])
    
    def simulate_video_conferencing_degradation(
        self, 
        image: np.ndarray, 
        scale_factor: int = 4,
        interpolation_down: str = 'bicubic',
        interpolation_up: str = 'bilinear'
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        Simulate video conferencing degradation by downscaling and upscaling.
        
        Args:
            image: Input high-resolution image
            scale_factor: Downscaling factor
            interpolation_down: Downscaling interpolation method
            interpolation_up: Upscaling interpolation method
            
        Returns:
            Tuple of (degraded_image, ground_truth)
        """
        h, w = image.shape[:2]
        
        # Downscale
        if interpolation_down == 'bicubic':
            down_interp = cv2.INTER_CUBIC
        elif interpolation_down == 'bilinear':
            down_interp = cv2.INTER_LINEAR
        else:
            down_interp = cv2.INTER_AREA
            
        low_res = cv2.resize(
            image, 
            (w // scale_factor, h // scale_factor), 
            interpolation=down_interp
        )
        
        # Upscale back
        if interpolation_up == 'bicubic':
            up_interp = cv2.INTER_CUBIC
        elif interpolation_up == 'bilinear':
            up_interp = cv2.INTER_LINEAR
        else:
            up_interp = cv2.INTER_LANCZOS4
            
        degraded = cv2.resize(low_res, (w, h), interpolation=up_interp)
        
        return degraded, image
    
    def extract_patches(
        self, 
        image: np.ndarray, 
        patch_size: Tuple[int, int],
        stride: Optional[int] = None
    ) -> List[np.ndarray]:
        """
        Extract patches from an image for training.
        
        Args:
            image: Input image
            patch_size: Size of patches to extract (height, width)
            stride: Stride for patch extraction (default: patch_size)
            
        Returns:
            List of image patches
        """
        if stride is None:
            stride = min(patch_size)
            
        h, w = image.shape[:2]
        patch_h, patch_w = patch_size
        
        patches = []
        for y in range(0, h - patch_h + 1, stride):
            for x in range(0, w - patch_w + 1, stride):
                patch = image[y:y+patch_h, x:x+patch_w]
                if patch.shape[:2] == patch_size:
                    patches.append(patch)
        
        return patches
    
    def process_single_image(
        self, 
        image_path: str,
        output_dir: str,
        base_name: str
    ) -> int:
        """
        Process a single image and generate training pairs.
        
        Args:
            image_path: Path to input image
            output_dir: Output directory for processed images
            base_name: Base name for output files
            
        Returns:
            Number of patches generated
        """
        # Load image
        image = cv2.imread(image_path)
        if image is None:
            print(f"Warning: Could not load image {image_path}")
            return 0
            
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Ensure minimum size
        h, w = image.shape[:2]
        min_size = max(self.crop_size) * 2
        if h < min_size or w < min_size:
            # Resize to minimum required size
            scale = min_size / min(h, w)
            new_h, new_w = int(h * scale), int(w * scale)
            image = cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_CUBIC)
        
        # Generate degraded version
        degraded, ground_truth = self.simulate_video_conferencing_degradation(image)
        
        # Extract patches
        gt_patches = self.extract_patches(ground_truth, self.crop_size)
        deg_patches = self.extract_patches(degraded, self.crop_size)
        
        # Save patches
        patch_count = 0
        for i, (gt_patch, deg_patch) in enumerate(zip(gt_patches, deg_patches)):
            # Apply augmentation
            augmented = self.augmentation(image=gt_patch, mask=deg_patch)
            gt_aug = augmented['image']
            deg_aug = augmented['mask']
            
            # Save ground truth patch
            gt_filename = f"{base_name}_gt_{i:04d}.png"
            gt_path = os.path.join(output_dir, "ground_truth", gt_filename)
            cv2.imwrite(gt_path, cv2.cvtColor(gt_aug, cv2.COLOR_RGB2BGR))
            
            # Save degraded patch
            deg_filename = f"{base_name}_deg_{i:04d}.png"
            deg_path = os.path.join(output_dir, "degraded", deg_filename)
            cv2.imwrite(deg_path, cv2.cvtColor(deg_aug, cv2.COLOR_RGB2BGR))
            
            patch_count += 1
        
        return patch_count
    
    def generate_dataset(
        self, 
        input_dir: str, 
        output_dir: str,
        categories: Optional[List[str]] = None
    ):
        """
        Generate complete training dataset from input directory.
        
        Args:
            input_dir: Directory containing source images
            output_dir: Output directory for processed dataset
            categories: List of image categories to process
        """
        # Create output directories
        os.makedirs(os.path.join(output_dir, "ground_truth"), exist_ok=True)
        os.makedirs(os.path.join(output_dir, "degraded"), exist_ok=True)
        
        # Supported image extensions
        extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        
        # Find all images
        image_paths = []
        for ext in extensions:
            image_paths.extend(Path(input_dir).rglob(f"*{ext}"))
            image_paths.extend(Path(input_dir).rglob(f"*{ext.upper()}"))
        
        print(f"Found {len(image_paths)} images to process")
        
        total_patches = 0
        for image_path in tqdm(image_paths, desc="Processing images"):
            base_name = image_path.stem
            patch_count = self.process_single_image(
                str(image_path), 
                output_dir, 
                base_name
            )
            total_patches += patch_count
        
        print(f"Generated {total_patches} training patches")
        
        # Save dataset info
        dataset_info = {
            'total_images': len(image_paths),
            'total_patches': total_patches,
            'patch_size': self.crop_size,
            'input_resolution': self.input_size,
            'target_resolution': self.target_size,
            'augmentation': self.config['data']['augmentation']
        }
        
        with open(os.path.join(output_dir, "dataset_info.yaml"), 'w') as f:
            yaml.dump(dataset_info, f, default_flow_style=False)


def main():
    parser = argparse.ArgumentParser(description="Generate training dataset for image sharpening")
    parser.add_argument("--input_dir", type=str, required=True, help="Input directory with source images")
    parser.add_argument("--output_dir", type=str, required=True, help="Output directory for processed dataset")
    parser.add_argument("--config", type=str, default="configs/config.yaml", help="Configuration file path")
    
    args = parser.parse_args()
    
    generator = DatasetGenerator(args.config)
    generator.generate_dataset(args.input_dir, args.output_dir)


if __name__ == "__main__":
    main()
