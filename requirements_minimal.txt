# Core Deep Learning and Computer Vision
torch>=2.0.0
torchvision>=0.15.0
opencv-python>=4.8.0
Pillow>=10.0.0
numpy>=1.24.0
scipy>=1.10.0

# Model Optimization (minimal)
onnx>=1.14.0
onnxruntime>=1.15.0

# Image Quality Metrics
scikit-image>=0.21.0
pytorch-msssim>=1.0.0

# Data Processing and Augmentation
albumentations>=1.3.0

# Visualization and UI
matplotlib>=3.7.0
seaborn>=0.12.0
streamlit>=1.25.0

# Utilities
tqdm>=4.65.0
tensorboard>=2.13.0
pyyaml>=6.0

# Video Processing
imageio>=2.31.0

# Development and Testing
pytest>=7.4.0
