# Image Sharpening for Video Conferencing using Knowledge Distillation

## Overview

This project implements a real-time image sharpening system designed to enhance video conferencing quality using knowledge distillation. The system uses a Teacher-Student model approach where a lightweight student model learns from a high-performing teacher model to achieve real-time performance (30-60 fps) while maintaining high image quality (SSIM > 90%).

## Features

- **Real-time Processing**: Optimized for 30-60 fps performance
- **Knowledge Distillation**: Lightweight student model guided by powerful teacher
- **High Quality**: Target SSIM score above 90%
- **Flexible Input**: Supports webcam capture and manual image upload
- **Multiple Formats**: Compatible with various image formats and resolutions
- **Benchmark Testing**: Comprehensive evaluation on 100+ diverse images

## Project Structure

```
imagesharpening/
├── src/
│   ├── models/          # Model architectures
│   ├── data/            # Data loading and preprocessing
│   ├── training/        # Training scripts and loops
│   ├── inference/       # Real-time inference pipeline
│   ├── evaluation/      # Metrics and benchmarking
│   └── utils/           # Utility functions
├── configs/             # Configuration files
├── datasets/            # Training and test data
│   ├── raw/            # Original high-resolution images
│   └── processed/      # Preprocessed training data
├── models/             # Saved model checkpoints
│   ├── teacher/        # Teacher model weights
│   └── student/        # Student model weights
├── tests/              # Unit tests
├── results/            # Evaluation results and outputs
└── requirements.txt    # Python dependencies
```

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd imagesharpening
```

2. Create a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

## Quick Start

### 1. Prepare Data
```bash
python src/data/prepare_dataset.py --input_dir datasets/raw --output_dir datasets/processed
```

### 2. Train the Model
```bash
python src/training/train_distillation.py --config configs/config.yaml
```

### 3. Run Real-time Inference
```bash
python src/inference/realtime_sharpening.py --model models/student/best_model.pth
```

### 4. Evaluate Performance
```bash
python src/evaluation/benchmark.py --model models/student/best_model.pth --test_dir datasets/test
```

## Configuration

The main configuration is in `configs/config.yaml`. Key parameters:

- **Model Settings**: Teacher/student architectures, layer sizes
- **Training**: Learning rates, loss weights, distillation parameters
- **Data**: Input/output resolutions, augmentation settings
- **Performance**: Target FPS, SSIM thresholds

## Performance Targets

- **Speed**: 30-60 fps on standard hardware
- **Quality**: SSIM > 90% compared to ground truth
- **Resolution**: Support for 1920x1080 output
- **Efficiency**: Lightweight model suitable for real-time applications

## Evaluation Metrics

- **SSIM**: Structural Similarity Index (target > 90%)
- **PSNR**: Peak Signal-to-Noise Ratio
- **LPIPS**: Learned Perceptual Image Patch Similarity
- **FPS**: Frames per second measurement
- **MOS**: Mean Opinion Score from subjective evaluation

## Usage Examples

### Webcam Real-time Processing
```python
from src.inference.realtime_sharpening import RealtimeSharpener

sharpener = RealtimeSharpener("models/student/best_model.pth")
sharpener.start_webcam_processing()
```

### Single Image Processing
```python
from src.inference.image_processor import ImageProcessor

processor = ImageProcessor("models/student/best_model.pth")
enhanced_image = processor.process_image("input.jpg")
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Teacher models based on state-of-the-art super-resolution architectures
- Knowledge distillation techniques adapted for real-time applications
- Evaluation metrics following standard image quality assessment practices
